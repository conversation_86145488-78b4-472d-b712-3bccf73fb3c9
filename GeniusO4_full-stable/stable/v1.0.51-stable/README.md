# ChartGenius Stable Release v1.0.51-stable

**Дата создания:** 25.06.2025  
**Статус:** Проверенная стабильная версия  
**Назначение:** Rollback точка для восстановления  

## 📋 Описание версии

Эта версия представляет собой стабильную рабочую конфигурацию ChartGenius после агрессивной оптимизации расходов GCP. Все сервисы протестированы и работают корректно.

## 🏗️ Архитектура

### Cloud Run Services:
- **chartgenius-api-working:** 0.25 CPU, 256Mi RAM
- **chartgenius-bot-working:** 0.125 CPU, 128Mi RAM  
- **chartgenius-frontend:** 0.125 CPU, 128Mi RAM

### Особенности конфигурации:
- Scale-to-zero (min-instances=0)
- CPU throttling включен
- Агрессивные timeout настройки
- Concurrency=1 для минимального потребления ресурсов

## 💰 Экономические показатели

- **Расходы:** $1.50/месяц (98.6% экономии)
- **Free Tier статус:** Все сервисы в пределах лимитов
- **Budget alerts:** Настроены на $5/месяц

## 🔧 Быстрое восстановление

Для восстановления этой конфигурации используйте:
```bash
./restore_stable_version.sh
```

## 📁 Содержимое архива

- `cloud_run_configs/` - Конфигурации всех Cloud Run сервисов
- `environment_vars/` - Переменные окружения
- `docker_configs/` - Dockerfile и конфигурации контейнеров
- `scripts/` - Скрипты развертывания и восстановления
- `docs/` - Документация и инструкции

## ⚠️ Важные замечания

1. Эта конфигурация оптимизирована для минимального использования
2. При высокой нагрузке может потребоваться увеличение ресурсов
3. Scale-to-zero может вызывать задержки при холодном старте
4. Рекомендуется мониторинг производительности

## 🔄 Процедура rollback

1. Остановить текущие сервисы
2. Восстановить конфигурации из архива
3. Развернуть сервисы с сохраненными настройками
4. Проверить работоспособность

## 📞 Контакты

При проблемах с восстановлением обращайтесь к документации или используйте emergency rollback процедуры.
