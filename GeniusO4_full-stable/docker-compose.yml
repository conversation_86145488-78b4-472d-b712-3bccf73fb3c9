version: "3.8"

services:
  redis:
    image: redis:7-alpine
    ports:
      - "${REDIS_PORT:-6379}:6379"
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-}
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  api:
    build:
      context: .
      dockerfile: backend/Dockerfile
    ports:
      - "${API_PORT:-8000}:8000"
    environment:
      - ENV_FILE=.env.docker
    volumes:
      - ./backend/dev_logs:/app/backend/dev_logs
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped

  frontend:
    build:
      context: .
      dockerfile: frontend/Dockerfile
    depends_on:
      - api
    ports:
      - "${FRONTEND_PORT:-5173}:80"
    environment:
      - ENV_FILE=.env.docker
    restart: unless-stopped

  bot:
    build:
      context: .
      dockerfile: bot/Dockerfile
    depends_on:
      - api
      - redis
    environment:
      - ENV_FILE=.env.docker
    restart: unless-stopped

volumes:
  redis_data:
