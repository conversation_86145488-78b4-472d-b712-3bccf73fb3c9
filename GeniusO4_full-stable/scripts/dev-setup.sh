#!/bin/bash

# ChartGenius v2.0 Development Setup Script

set -e

echo "🚀 ChartGenius v2.0 Development Setup"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Check if running on Windows (Git Bash, WSL, etc.)
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    IS_WINDOWS=true
else
    IS_WINDOWS=false
fi

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check system requirements
print_step "Checking system requirements..."

# Check Python
if command_exists python; then
    PYTHON_VERSION=$(python --version 2>&1 | cut -d' ' -f2)
    print_status "Python found: $PYTHON_VERSION"
elif command_exists python3; then
    PYTHON_VERSION=$(python3 --version 2>&1 | cut -d' ' -f2)
    print_status "Python3 found: $PYTHON_VERSION"
    alias python=python3
else
    print_error "Python not found. Please install Python 3.8+"
    exit 1
fi

# Check Node.js
if command_exists node; then
    NODE_VERSION=$(node --version)
    print_status "Node.js found: $NODE_VERSION"
else
    print_error "Node.js not found. Please install Node.js 18+"
    exit 1
fi

# Check npm
if command_exists npm; then
    NPM_VERSION=$(npm --version)
    print_status "npm found: $NPM_VERSION"
else
    print_error "npm not found. Please install npm"
    exit 1
fi

# Check Docker
if command_exists docker; then
    DOCKER_VERSION=$(docker --version | cut -d' ' -f3 | cut -d',' -f1)
    print_status "Docker found: $DOCKER_VERSION"
else
    print_warning "Docker not found. Redis will need to be installed manually"
fi

# Check Docker Compose
if command_exists docker-compose; then
    COMPOSE_VERSION=$(docker-compose --version | cut -d' ' -f3 | cut -d',' -f1)
    print_status "Docker Compose found: $COMPOSE_VERSION"
elif command_exists docker && docker compose version >/dev/null 2>&1; then
    COMPOSE_VERSION=$(docker compose version | cut -d' ' -f3)
    print_status "Docker Compose (plugin) found: $COMPOSE_VERSION"
    alias docker-compose="docker compose"
else
    print_warning "Docker Compose not found"
fi

print_step "Setting up environment file..."

# Create .env from template if it doesn't exist
if [ ! -f .env ]; then
    if [ -f .env.example ]; then
        cp .env.example .env
        print_status ".env file created from template"
        print_warning "Please edit .env file with your configuration before continuing"
    else
        print_error ".env.example not found"
        exit 1
    fi
else
    print_status ".env file already exists"
fi

print_step "Installing backend dependencies..."

# Install Python dependencies
cd backend
if [ -f requirements.txt ]; then
    # Create virtual environment if it doesn't exist
    if [ ! -d "venv" ]; then
        print_status "Creating Python virtual environment..."
        python -m venv venv
    fi
    
    # Activate virtual environment
    if [ "$IS_WINDOWS" = true ]; then
        source venv/Scripts/activate
    else
        source venv/bin/activate
    fi
    
    print_status "Installing Python packages..."
    pip install --upgrade pip
    pip install -r requirements.txt
    
    print_status "Backend dependencies installed"
else
    print_error "requirements.txt not found in backend directory"
    exit 1
fi

cd ..

print_step "Installing frontend dependencies..."

# Install Node.js dependencies
cd frontend
if [ -f package.json ]; then
    print_status "Installing Node.js packages..."
    npm install
    print_status "Frontend dependencies installed"
else
    print_error "package.json not found in frontend directory"
    exit 1
fi

cd ..

print_step "Setting up Redis..."

# Start Redis with Docker if available
if command_exists docker-compose; then
    print_status "Starting Redis with Docker Compose..."
    docker-compose up -d redis
    
    # Wait for Redis to be ready
    print_status "Waiting for Redis to be ready..."
    sleep 5
    
    if docker-compose exec redis redis-cli ping >/dev/null 2>&1; then
        print_status "Redis is running and ready"
    else
        print_warning "Redis may not be ready yet"
    fi
else
    print_warning "Docker Compose not available. Please install Redis manually"
    print_warning "Redis installation instructions:"
    if [ "$IS_WINDOWS" = true ]; then
        print_warning "  - Download Redis for Windows from https://github.com/microsoftarchive/redis/releases"
        print_warning "  - Or use WSL2 with Linux Redis"
    else
        print_warning "  - Ubuntu/Debian: sudo apt-get install redis-server"
        print_warning "  - CentOS/RHEL: sudo yum install redis"
        print_warning "  - macOS: brew install redis"
    fi
fi

print_step "Setting up Oracle AJD connection..."

# Check if Oracle environment variables are set
if grep -q "ORACLE_USERNAME=" .env && grep -q "ORACLE_PASSWORD=" .env; then
    print_status "Oracle AJD configuration found in .env"
    
    # Test Oracle connection
    print_status "Testing Oracle AJD connection..."
    cd backend
    if [ "$IS_WINDOWS" = true ]; then
        source venv/Scripts/activate
    else
        source venv/bin/activate
    fi
    
    python -c "
try:
    from services.oracle_client import oracle_client
    print('✅ Oracle AJD connection test passed')
except Exception as e:
    print(f'⚠️  Oracle AJD connection test failed: {e}')
    print('Please check your Oracle configuration in .env file')
" || print_warning "Oracle AJD connection test failed"
    
    cd ..
else
    print_warning "Oracle AJD configuration not found in .env"
    print_warning "Please add Oracle AJD credentials to .env file:"
    print_warning "  ORACLE_USERNAME=your_username"
    print_warning "  ORACLE_PASSWORD=your_password"
    print_warning "  ORACLE_DSN=your_connection_string"
fi

print_step "Creating necessary directories..."

# Create log directories
mkdir -p backend/dev_logs
mkdir -p logs
mkdir -p backups

print_status "Directories created"

print_step "Setting up Git hooks (optional)..."

# Setup pre-commit hooks if .git exists
if [ -d .git ]; then
    cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
echo "Running pre-commit checks..."

# Check Python code style
cd backend
python -m flake8 . --max-line-length=120 --exclude=migrations,venv
if [ $? -ne 0 ]; then
    echo "❌ Python linting failed"
    exit 1
fi
cd ..

# Check frontend code
cd frontend
npm run lint
if [ $? -ne 0 ]; then
    echo "❌ Frontend linting failed"
    exit 1
fi
cd ..

echo "✅ Pre-commit checks passed"
EOF
    
    chmod +x .git/hooks/pre-commit
    print_status "Git pre-commit hook installed"
else
    print_warning "Not a Git repository, skipping Git hooks setup"
fi

print_step "Development setup completed!"

echo ""
echo "🎉 ChartGenius v2.0 development environment is ready!"
echo ""
echo "Next steps:"
echo "1. Edit .env file with your configuration (Oracle AJD, API keys, etc.)"
echo "2. Run migration: make migrate (if you have existing Firestore data)"
echo "3. Start development: make dev"
echo ""
echo "Available commands:"
echo "  make dev      - Start development servers"
echo "  make test     - Run tests"
echo "  make lint     - Run linting"
echo "  make clean    - Clean temporary files"
echo "  make help     - Show all available commands"
echo ""
echo "Development URLs:"
echo "  Backend:  http://localhost:8000"
echo "  Frontend: http://localhost:5173"
echo "  API Docs: http://localhost:8000/docs"
echo ""
print_status "Happy coding! 🚀"
