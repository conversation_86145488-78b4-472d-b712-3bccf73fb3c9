# Tailwind CSS 4.0 - Основные концепции

## Utility-First подход

Tailwind CSS использует подход "utility-first", где стили применяются через небольшие, одноцелевые классы.

### Пример:
```html
<h1 class="text-3xl font-bold underline">
  Hello world!
</h1>
```

## Responsive Design

### Breakpoints:
- `sm:` - 640px и выше
- `md:` - 768px и выше  
- `lg:` - 1024px и выше
- `xl:` - 1280px и выше
- `2xl:` - 1536px и выше

### Пример:
```html
<div class="text-base md:text-lg lg:text-xl">
  Responsive text
</div>
```

## Hover, Focus и другие состояния

### Псевдоклассы:
- `hover:` - при наведении
- `focus:` - при фокусе
- `active:` - при активации
- `disabled:` - для отключенных элементов
- `first:` - первый элемент
- `last:` - последний элемент

### Пример:
```html
<button class="bg-blue-500 hover:bg-blue-700 focus:ring-2 focus:ring-blue-300">
  Button
</button>
```

## Dark Mode

### Автоматический режим:
```html
<div class="bg-white dark:bg-gray-800 text-black dark:text-white">
  Content
</div>
```

### Ручное управление:
```html
<html class="dark">
  <!-- Контент -->
</html>
```

## Theme Variables

В Tailwind CSS 4.0 можно использовать CSS переменные для кастомизации:

```css
@import "tailwindcss";

:root {
  --color-primary: #3b82f6;
  --color-secondary: #64748b;
}
```

## Colors

### Основные цвета:
- `slate`, `gray`, `zinc`, `neutral`, `stone`
- `red`, `orange`, `amber`, `yellow`, `lime`, `green`
- `emerald`, `teal`, `cyan`, `sky`, `blue`, `indigo`
- `violet`, `purple`, `fuchsia`, `pink`, `rose`

### Оттенки:
- `50` - самый светлый
- `100`, `200`, `300`, `400`, `500` - средние
- `600`, `700`, `800`, `900`, `950` - самый темный

### Пример:
```html
<div class="bg-blue-500 text-white">
  Blue background
</div>
```

## Layout

### Display:
- `block`, `inline-block`, `inline`
- `flex`, `inline-flex`
- `grid`, `inline-grid`
- `hidden`

### Flexbox:
- `flex-row`, `flex-col`
- `justify-start`, `justify-center`, `justify-end`, `justify-between`
- `items-start`, `items-center`, `items-end`

### Grid:
- `grid-cols-1` до `grid-cols-12`
- `col-span-1` до `col-span-12`
- `gap-1` до `gap-96`

## Spacing

### Padding и Margin:
- `p-0` до `p-96` (padding)
- `m-0` до `m-96` (margin)
- `px-`, `py-`, `pt-`, `pr-`, `pb-`, `pl-` (направления)

### Размеры:
- `0` = 0px
- `1` = 0.25rem (4px)
- `2` = 0.5rem (8px)
- `4` = 1rem (16px)
- `8` = 2rem (32px)
- `16` = 4rem (64px)

## Typography

### Font Size:
- `text-xs` (12px)
- `text-sm` (14px)
- `text-base` (16px)
- `text-lg` (18px)
- `text-xl` (20px)
- `text-2xl` (24px)
- `text-3xl` (30px)

### Font Weight:
- `font-thin` (100)
- `font-light` (300)
- `font-normal` (400)
- `font-medium` (500)
- `font-semibold` (600)
- `font-bold` (700)
- `font-extrabold` (800)
- `font-black` (900)

### Text Alignment:
- `text-left`, `text-center`, `text-right`, `text-justify`

## Borders

### Border Width:
- `border` (1px)
- `border-0`, `border-2`, `border-4`, `border-8`

### Border Radius:
- `rounded-none`, `rounded-sm`, `rounded`, `rounded-md`
- `rounded-lg`, `rounded-xl`, `rounded-2xl`, `rounded-3xl`
- `rounded-full`

### Border Color:
```html
<div class="border border-gray-300 rounded-lg">
  Content
</div>
```

## Effects

### Box Shadow:
- `shadow-sm`, `shadow`, `shadow-md`, `shadow-lg`
- `shadow-xl`, `shadow-2xl`, `shadow-inner`, `shadow-none`

### Opacity:
- `opacity-0` до `opacity-100` (с шагом 5)

### Transitions:
- `transition` - все свойства
- `transition-colors` - только цвета
- `transition-transform` - только трансформации
- `duration-75` до `duration-1000` - длительность

### Пример:
```html
<button class="transition duration-300 hover:shadow-lg transform hover:scale-105">
  Animated Button
</button>
```
