# Tailwind CSS 4.0 - Установка и настройка

## Ключевые изменения в Tailwind CSS 4.0

Tailwind CSS 4.0 имеет кардинально новую архитектуру по сравнению с версией 3.x:

### Основные отличия:
1. **Новый плагин для Vite**: `@tailwindcss/vite` вместо PostCSS плагина
2. **Упрощенный импорт**: `@import "tailwindcss";` вместо отдельных слоев
3. **Нет файла конфигурации**: tailwind.config.js больше не нужен
4. **Автоматическое сканирование**: автоматически находит классы в файлах

## Установка для Vite проектов

### 1. Установка пакетов
```bash
npm install tailwindcss @tailwindcss/vite
```

### 2. Настройка Vite
В `vite.config.js`:
```javascript
import { defineConfig } from 'vite'
import tailwindcss from '@tailwindcss/vite'

export default defineConfig({
  plugins: [
    tailwindcss(),
  ],
})
```

### 3. Импорт в CSS
В основном CSS файле:
```css
@import "tailwindcss";
```

## Установка для Next.js

### 1. Установка пакетов
```bash
npm install tailwindcss @tailwindcss/postcss postcss
```

### 2. Настройка PostCSS
Создать `postcss.config.mjs`:
```javascript
const config = {
  plugins: {
    "@tailwindcss/postcss": {},
  },
};
export default config;
```

### 3. Импорт в globals.css
```css
@import "tailwindcss";
```

## Установка через CLI

### 1. Установка
```bash
npm install tailwindcss @tailwindcss/cli
```

### 2. Создание CSS файла
В `src/input.css`:
```css
@import "tailwindcss";
```

### 3. Сборка
```bash
npx @tailwindcss/cli -i ./src/input.css -o ./src/output.css --watch
```

## Миграция с версии 3.x

### Что нужно удалить:
1. `tailwind.config.js` файл
2. Старые PostCSS плагины
3. Отдельные импорты слоев (@tailwind base, components, utilities)

### Что нужно изменить:
1. Установить новые пакеты
2. Обновить конфигурацию сборщика
3. Заменить импорты на `@import "tailwindcss";`

## Совместимость

### Поддерживаемые сборщики:
- Vite (рекомендуется)
- Next.js
- PostCSS
- CLI
- Laravel
- Nuxt
- SvelteKit
- Angular
- и другие

### Требования:
- Node.js 18+
- Современные браузеры
- ES modules поддержка

## Отладка

### Проверка установки:
1. Убедитесь что установлена только одна версия Tailwind
2. Проверьте правильность импорта в CSS
3. Убедитесь что плагин добавлен в конфигурацию сборщика

### Частые проблемы:
1. **Конфликт версий**: удалите старую версию tailwindcss
2. **Неправильный импорт**: используйте `@import "tailwindcss";`
3. **Отсутствие плагина**: добавьте плагин в конфигурацию сборщика
