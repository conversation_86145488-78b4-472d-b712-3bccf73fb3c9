# Инструкции Codex

## Формат коммитов
Используйте стиль Conventional Commits. Сообщение состоит из типа (`feat`, `fix`, `docs`, и т.д.), за которым следует двоеточие и краткое описание изменений. Например:

```
feat: добавлен AGENTS.md с базовыми правилами
```

## Запуск тестов
Перед выполнением тестов создайте новое виртуальное окружение и установите зависимости проекта. Далее запустите:

```
pytest
npm test
```

Возможно, некоторые зависимости (например, `google-cloud-firestore`, `scipy`) не установятся в среде Codex. В этом случае часть тестов может быть пропущена.
