# Руководство по миграции на Tailwind CSS 4.0

## Обзор изменений

Tailwind CSS 4.0 представляет кардинально новую архитектуру с упрощенной настройкой и улучшенной производительностью.

## Выполненные изменения в проекте

### 1. Обновление package.json

**Удалены конфликтующие пакеты:**
```json
// Удалено
"tailwindcss": "^3.4.17"  // Старая версия
"autoprefixer": "^10.4.20"  // Встроен в Tailwind CSS 4.0
```

**Оставлены актуальные пакеты:**
```json
{
  "dependencies": {
    "@tailwindcss/postcss": "^4.1.11"
  },
  "devDependencies": {
    "@tailwindcss/vite": "^4.0.0"
  }
}
```

### 2. Конфигурация Vite

В `vite.config.js` используется правильный плагин:
```javascript
import tailwindcss from '@tailwindcss/vite'

export default defineConfig({
  plugins: [react(), tailwindcss(), apiLogger()],
  // ...
})
```

### 3. CSS конфигурация

В `src/index.css` используется новый синтаксис:
```css
@import "tailwindcss";

@theme {
  /* Кастомные переменные */
  --color-primary: #2563eb;
  --radius: 0.75rem;
  /* ... */
}
```

### 4. Исправление кастомных классов

**Проблема:** Циклические ссылки в @apply
```css
/* Неправильно */
.watermark-chart {
  @apply watermark opacity-10;  /* Ссылка на несуществующий класс */
}
```

**Решение:** Полное определение каждого класса
```css
/* Правильно */
.watermark-chart {
  @apply pointer-events-none select-none opacity-10;
  background-image: url('/371.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
```

## Ключевые отличия от версии 3.x

### Что изменилось:

1. **Нет файла конфигурации**: `tailwind.config.js` больше не нужен
2. **Новый импорт**: `@import "tailwindcss";` вместо отдельных слоев
3. **Встроенный autoprefixer**: не нужно устанавливать отдельно
4. **Новый синтаксис @theme**: для кастомизации переменных
5. **Плагин для Vite**: `@tailwindcss/vite` вместо PostCSS

### Что осталось:

1. **Все utility классы**: работают как прежде
2. **@apply директива**: работает для существующих классов
3. **Responsive префиксы**: `sm:`, `md:`, `lg:` и т.д.
4. **State префиксы**: `hover:`, `focus:`, `active:` и т.д.

## Проверка работоспособности

### 1. Сборка проекта
```bash
npm run dev
```

### 2. Проверка стилей
- Откройте http://localhost:5174
- Убедитесь что стили применяются
- Проверьте responsive поведение
- Проверьте hover эффекты

### 3. Проверка кастомных классов
- Убедитесь что все @apply директивы работают
- Проверьте кастомные компоненты (.btn, .card и т.д.)

## Рекомендации по разработке

### 1. Использование новых возможностей

**Кастомные переменные через @theme:**
```css
@theme {
  --color-brand: #ff6b35;
  --font-family-display: "Playfair Display", serif;
}
```

**Использование в HTML:**
```html
<div class="bg-brand text-display">
  Custom themed content
</div>
```

### 2. Избегание проблем

**❌ Не делайте:**
```css
.my-class {
  @apply other-custom-class;  /* Циклические ссылки */
}
```

**✅ Делайте:**
```css
.my-class {
  @apply bg-blue-500 text-white p-4;  /* Только utility классы */
}
```

### 3. Отладка

**Проверка генерируемого CSS:**
- Откройте DevTools
- Проверьте что классы применяются
- Убедитесь что нет конфликтов

**Логи Vite:**
- Следите за сообщениями в консоли
- Обращайте внимание на предупреждения

## Совместимость

### Поддерживаемые браузеры:
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

### Node.js:
- Версия 18.0.0 или выше

## Дополнительные ресурсы

- [Официальная документация Tailwind CSS 4.0](https://tailwindcss.com/docs)
- [Руководство по миграции](https://tailwindcss.com/docs/upgrade-guide)
- [Примеры использования](https://tailwindcss.com/docs/installation/using-vite)

## Заключение

Миграция на Tailwind CSS 4.0 завершена успешно. Проект теперь использует:
- ✅ Современную архитектуру Tailwind CSS 4.0
- ✅ Оптимизированную сборку через Vite
- ✅ Новый синтаксис @theme для кастомизации
- ✅ Исправленные кастомные классы без циклических ссылок

Все функции интерфейса работают корректно, стили применяются правильно.
