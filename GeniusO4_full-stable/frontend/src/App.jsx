import React from 'react'
import { Routes, Route, Link } from 'react-router-dom'
import './index.css'
import './styles/professionalTheme.css'
import Home from './pages/Home'
import About from './pages/About'
import AdminPanel from './pages/AdminPanel'
import UserDashboard from './pages/UserDashboard'

export default function App() {
  return (
    <div className="pro-theme min-h-screen">
      {/* Professional Header - Compact */}
      <header className="pro-header">
        <div className="flex justify-between items-center">
          <div className="flex items-center pro-space-x-4">
            <h1 className="text-xl font-semibold pro-text-primary">ChartGenius</h1>
            <span className="text-xs pro-text-muted">v2.0</span>
          </div>
          <nav className="flex items-center pro-space-x-4">
            <Link to="/" className="text-sm pro-text-secondary hover:pro-text-primary">Главная</Link>
            <Link to="/about" className="text-sm pro-text-secondary hover:pro-text-primary">О системе</Link>
            <Link to="/dashboard" className="text-sm pro-text-secondary hover:pro-text-primary">Кабинет</Link>
            <Link to="/admin" className="text-sm pro-text-secondary hover:pro-text-primary">Админ</Link>
            <div className="flex items-center pro-space-x-2">
              <span className="pro-status pro-status-success">Готов</span>
              <span className="pro-status pro-status-info">Live</span>
            </div>
          </nav>
        </div>
      </header>

      {/* Main Content - Full Height */}
      <main className="pro-main-content">
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/about" element={<About />} />
          <Route path="/admin" element={<AdminPanel />} />
          <Route path="/dashboard" element={<UserDashboard />} />
        </Routes>
      </main>
    </div>
  )
}
