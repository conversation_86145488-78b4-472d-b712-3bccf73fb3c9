import React from 'react'
import { 
  TrendingUp, 
  TrendingDown, 
  Activity,
  BarChart3,
  Zap,
  Target
} from 'lucide-react'
import { cn } from '../lib/utils'
import { parseAnalysisData, extractKeyIndicators } from '../utils/analysisDataParser'

/**
 * Key Indicators Widget for ChartGenius v2.0
 * Displays RSI, MACD, ATR, ADX and other key technical indicators
 * from real analysis data
 */
export const KeyIndicatorsWidget = ({ 
  rawApiResponse = null,
  isLoading = false,
  className = '',
  ...props 
}) => {
  // Extract indicators from real API data
  const getIndicators = () => {
    if (!rawApiResponse) return null
    
    const analysisData = parseAnalysisData(rawApiResponse)
    if (!analysisData) return null
    
    return extractKeyIndicators(analysisData)
  }

  const indicators = getIndicators()

  // Get trend styling based on trend text
  const getTrendStyling = (trend) => {
    if (!trend) return { color: 'pro-text-muted', icon: Activity }
    
    const trendText = trend.toLowerCase()
    
    if (trendText.includes('медвеж') || trendText.includes('нисходящ') || trendText.includes('bearish')) {
      return { color: 'pro-danger', icon: TrendingDown }
    }
    
    if (trendText.includes('бычий') || trendText.includes('восходящ') || trendText.includes('bullish')) {
      return { color: 'pro-success', icon: TrendingUp }
    }
    
    if (trendText.includes('сильный') || trendText.includes('strong')) {
      return { color: 'pro-info', icon: Zap }
    }
    
    return { color: 'pro-warning', icon: Activity }
  }

  // Format indicator value
  const formatIndicatorValue = (value, type = 'number') => {
    if (value === null || value === undefined || isNaN(value)) return 'N/A'
    
    switch (type) {
      case 'percentage':
        return `${Math.round(value)}%`
      case 'decimal':
        return value.toFixed(2)
      default:
        return Math.round(value).toLocaleString()
    }
  }

  if (isLoading) {
    return (
      <div 
        className={cn(
          'pro-panel-compact border-l-4 border-l-pro-info',
          className
        )}
        {...props}
      >
        <div className="flex items-center space-x-2 mb-3">
          <BarChart3 size={16} className="text-pro-info animate-pulse" />
          <h4 className="text-sm font-medium text-pro-text-primary">
            Ключевые индикаторы
          </h4>
        </div>
        
        <div className="space-y-2">
          <div className="h-4 bg-pro-bg-tertiary rounded animate-pulse"></div>
          <div className="h-3 bg-pro-bg-tertiary rounded w-3/4 animate-pulse"></div>
          <div className="h-3 bg-pro-bg-tertiary rounded w-1/2 animate-pulse"></div>
        </div>
      </div>
    )
  }

  if (!indicators) {
    return (
      <div 
        className={cn(
          'pro-panel-compact border-l-4 border-l-pro-text-muted',
          className
        )}
        {...props}
      >
        <div className="flex items-center space-x-2 mb-3">
          <BarChart3 size={16} className="text-pro-text-muted" />
          <h4 className="text-sm font-medium text-pro-text-primary">
            Ключевые индикаторы
          </h4>
        </div>
        
        <div className="text-xs text-pro-text-secondary">
          Данные индикаторов недоступны
        </div>
      </div>
    )
  }

  return (
    <div 
      className={cn(
        'pro-panel-compact border-l-4 border-l-pro-info',
        className
      )}
      {...props}
    >
      {/* Header */}
      <div className="flex items-center space-x-2 mb-3">
        <BarChart3 size={16} className="text-pro-info" />
        <h4 className="text-sm font-medium text-pro-text-primary">
          Ключевые индикаторы
        </h4>
      </div>

      {/* Indicators Grid */}
      <div className="space-y-3">
        {/* RSI */}
        {indicators.rsi && (
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Target size={12} className={`text-${getTrendStyling(indicators.rsi.trend).color}`} />
              <span className="text-xs font-medium text-pro-text-primary">RSI</span>
            </div>
            <div className="text-right">
              <div className={`text-xs font-semibold text-${getTrendStyling(indicators.rsi.trend).color}`}>
                {formatIndicatorValue(indicators.rsi.value)}
              </div>
              <div className="text-xs text-pro-text-muted">
                {indicators.rsi.trend}
              </div>
            </div>
          </div>
        )}

        {/* MACD */}
        {indicators.macd && (
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Activity size={12} className={`text-${getTrendStyling(indicators.macd.trend).color}`} />
              <span className="text-xs font-medium text-pro-text-primary">MACD</span>
            </div>
            <div className="text-right">
              <div className={`text-xs font-semibold text-${getTrendStyling(indicators.macd.trend).color}`}>
                {formatIndicatorValue(indicators.macd.value)}
              </div>
              <div className="text-xs text-pro-text-muted">
                Signal: {formatIndicatorValue(indicators.macd.signal)}
              </div>
            </div>
          </div>
        )}

        {/* ATR */}
        {indicators.atr && (
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Zap size={12} className={`text-${getTrendStyling(indicators.atr.trend).color}`} />
              <span className="text-xs font-medium text-pro-text-primary">ATR</span>
            </div>
            <div className="text-right">
              <div className={`text-xs font-semibold text-${getTrendStyling(indicators.atr.trend).color}`}>
                {formatIndicatorValue(indicators.atr.value)}
              </div>
              <div className="text-xs text-pro-text-muted">
                {indicators.atr.trend}
              </div>
            </div>
          </div>
        )}

        {/* ADX */}
        {indicators.adx && (
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <TrendingUp size={12} className={`text-${getTrendStyling(indicators.adx.trend).color}`} />
              <span className="text-xs font-medium text-pro-text-primary">ADX</span>
            </div>
            <div className="text-right">
              <div className={`text-xs font-semibold text-${getTrendStyling(indicators.adx.trend).color}`}>
                {formatIndicatorValue(indicators.adx.value, 'decimal')}
              </div>
              <div className="text-xs text-pro-text-muted">
                {indicators.adx.trend}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Additional Info */}
      <div className="mt-3 pt-2 border-t border-pro-border-light">
        <div className="text-xs text-pro-text-muted text-center">
          Обновлено из анализа модели
        </div>
      </div>
    </div>
  )
}

export default KeyIndicatorsWidget
