import React, { useState } from 'react'
import * as Accordion from '@radix-ui/react-accordion'
import { 
  ChevronDown,
  TrendingUp,
  Settings,
  BarChart3,
  Zap,
  Eye,
  Bitcoin,
  DollarSign,
  Percent,
  <PERSON>,
  Target
} from 'lucide-react'
import { cn } from '../lib/utils'

/**
 * Sidebar component with 5 accordions as per TZ requirements
 * 1. Symbols & Markets
 * 2. Timeframes & Intervals  
 * 3. Technical Indicators
 * 4. AI & Analysis
 * 5. Watchlist & Alerts
 */
export const Sidebar = ({ collapsed, onToggle }) => {
  const [openItems, setOpenItems] = useState(['symbols']) // Default open

  if (collapsed) {
    return (
      <div className="h-full w-16 flex flex-col items-center py-4 space-y-4">
        <button
          onClick={onToggle}
          className="p-2 hover:bg-accent rounded-md transition-colors"
          title="Expand sidebar"
        >
          <BarChart3 size={20} />
        </button>
        
        {/* Collapsed icons */}
        <div className="flex flex-col space-y-2">
          <div className="p-2 rounded-md bg-primary/10" title="Symbols">
            <Bitcoin size={16} className="text-primary" />
          </div>
          <div className="p-2 rounded-md" title="Timeframes">
            <Clock size={16} className="text-muted-foreground" />
          </div>
          <div className="p-2 rounded-md" title="Indicators">
            <TrendingUp size={16} className="text-muted-foreground" />
          </div>
          <div className="p-2 rounded-md" title="AI Analysis">
            <Zap size={16} className="text-muted-foreground" />
          </div>
          <div className="p-2 rounded-md" title="Watchlist">
            <Eye size={16} className="text-muted-foreground" />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-border">
        <h2 className="text-lg font-semibold text-foreground">Analysis Tools</h2>
        <p className="text-sm text-muted-foreground">Configure your trading setup</p>
      </div>

      {/* Accordion Content */}
      <div className="flex-1 overflow-y-auto">
        <Accordion.Root 
          type="multiple" 
          value={openItems}
          onValueChange={setOpenItems}
          className="w-full"
        >
          {/* 1. Symbols & Markets */}
          <Accordion.Item value="symbols" className="border-b border-border">
            <Accordion.Trigger className="flex h-12 w-full items-center justify-between px-4 py-2 text-left text-sm font-medium hover:bg-accent transition-colors [&[data-state=open]>svg]:rotate-180">
              <div className="flex items-center space-x-2">
                <Bitcoin size={16} className="text-primary" />
                <span>Symbols & Markets</span>
              </div>
              <ChevronDown size={16} className="transition-transform duration-200" />
            </Accordion.Trigger>
            <Accordion.Content className="overflow-hidden data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down">
              <div className="p-4 space-y-3">
                {/* Symbol Search */}
                <div>
                  <label className="text-xs font-medium text-muted-foreground">Search Symbol</label>
                  <input 
                    type="text" 
                    placeholder="BTC, ETH, AAPL..."
                    className="input mt-1"
                  />
                </div>
                
                {/* Popular Symbols */}
                <div>
                  <label className="text-xs font-medium text-muted-foreground">Popular</label>
                  <div className="mt-2 grid grid-cols-2 gap-2">
                    {['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT'].map(symbol => (
                      <button 
                        key={symbol}
                        className="p-2 text-xs bg-accent hover:bg-accent/80 rounded-md transition-colors"
                      >
                        {symbol}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Market Categories */}
                <div>
                  <label className="text-xs font-medium text-muted-foreground">Categories</label>
                  <div className="mt-2 space-y-1">
                    {['Crypto', 'Forex', 'Stocks', 'Commodities'].map(category => (
                      <button 
                        key={category}
                        className="w-full p-2 text-xs text-left hover:bg-accent rounded-md transition-colors"
                      >
                        {category}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </Accordion.Content>
          </Accordion.Item>

          {/* 2. Timeframes & Intervals */}
          <Accordion.Item value="timeframes" className="border-b border-border">
            <Accordion.Trigger className="flex h-12 w-full items-center justify-between px-4 py-2 text-left text-sm font-medium hover:bg-accent transition-colors [&[data-state=open]>svg]:rotate-180">
              <div className="flex items-center space-x-2">
                <Clock size={16} className="text-blue-500" />
                <span>Timeframes</span>
              </div>
              <ChevronDown size={16} className="transition-transform duration-200" />
            </Accordion.Trigger>
            <Accordion.Content className="overflow-hidden data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down">
              <div className="p-4 space-y-3">
                {/* Quick Timeframes */}
                <div>
                  <label className="text-xs font-medium text-muted-foreground">Quick Select</label>
                  <div className="mt-2 grid grid-cols-3 gap-2">
                    {['1m', '5m', '15m', '1h', '4h', '1d'].map(tf => (
                      <button 
                        key={tf}
                        className="p-2 text-xs bg-accent hover:bg-accent/80 rounded-md transition-colors"
                      >
                        {tf}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Custom Range */}
                <div>
                  <label className="text-xs font-medium text-muted-foreground">Custom Range</label>
                  <div className="mt-2 space-y-2">
                    <input 
                      type="number" 
                      placeholder="Days back"
                      className="input"
                    />
                    <select className="input">
                      <option>Last 7 days</option>
                      <option>Last 30 days</option>
                      <option>Last 90 days</option>
                      <option>Last year</option>
                    </select>
                  </div>
                </div>
              </div>
            </Accordion.Content>
          </Accordion.Item>

          {/* 3. Technical Indicators */}
          <Accordion.Item value="indicators" className="border-b border-border">
            <Accordion.Trigger className="flex h-12 w-full items-center justify-between px-4 py-2 text-left text-sm font-medium hover:bg-accent transition-colors [&[data-state=open]>svg]:rotate-180">
              <div className="flex items-center space-x-2">
                <TrendingUp size={16} className="text-green-500" />
                <span>Technical Indicators</span>
              </div>
              <ChevronDown size={16} className="transition-transform duration-200" />
            </Accordion.Trigger>
            <Accordion.Content className="overflow-hidden data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down">
              <div className="p-4 space-y-3">
                {/* Trend Indicators */}
                <div>
                  <label className="text-xs font-medium text-muted-foreground">Trend</label>
                  <div className="mt-2 space-y-1">
                    {[
                      { name: 'Moving Average', enabled: true },
                      { name: 'MACD', enabled: false },
                      { name: 'Bollinger Bands', enabled: true },
                      { name: 'Ichimoku', enabled: false }
                    ].map(indicator => (
                      <label key={indicator.name} className="flex items-center space-x-2">
                        <input 
                          type="checkbox" 
                          defaultChecked={indicator.enabled}
                          className="rounded border-border"
                        />
                        <span className="text-xs">{indicator.name}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Oscillators */}
                <div>
                  <label className="text-xs font-medium text-muted-foreground">Oscillators</label>
                  <div className="mt-2 space-y-1">
                    {[
                      { name: 'RSI', enabled: true },
                      { name: 'Stochastic', enabled: false },
                      { name: 'Williams %R', enabled: false }
                    ].map(indicator => (
                      <label key={indicator.name} className="flex items-center space-x-2">
                        <input 
                          type="checkbox" 
                          defaultChecked={indicator.enabled}
                          className="rounded border-border"
                        />
                        <span className="text-xs">{indicator.name}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            </Accordion.Content>
          </Accordion.Item>

          {/* 4. AI & Analysis */}
          <Accordion.Item value="ai" className="border-b border-border">
            <Accordion.Trigger className="flex h-12 w-full items-center justify-between px-4 py-2 text-left text-sm font-medium hover:bg-accent transition-colors [&[data-state=open]>svg]:rotate-180">
              <div className="flex items-center space-x-2">
                <Zap size={16} className="text-yellow-500" />
                <span>AI & Analysis</span>
              </div>
              <ChevronDown size={16} className="transition-transform duration-200" />
            </Accordion.Trigger>
            <Accordion.Content className="overflow-hidden data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down">
              <div className="p-4 space-y-3">
                {/* LLM Provider */}
                <div>
                  <label className="text-xs font-medium text-muted-foreground">LLM Provider</label>
                  <select className="input mt-1">
                    <option value="openai">OpenAI GPT-4</option>
                    <option value="gemini">Google Gemini</option>
                  </select>
                </div>

                {/* Analysis Type */}
                <div>
                  <label className="text-xs font-medium text-muted-foreground">Analysis Type</label>
                  <div className="mt-2 space-y-1">
                    {[
                      { name: 'Technical Analysis', enabled: true },
                      { name: 'Sentiment Analysis', enabled: false },
                      { name: 'Pattern Recognition', enabled: true },
                      { name: 'Risk Assessment', enabled: false }
                    ].map(analysis => (
                      <label key={analysis.name} className="flex items-center space-x-2">
                        <input 
                          type="checkbox" 
                          defaultChecked={analysis.enabled}
                          className="rounded border-border"
                        />
                        <span className="text-xs">{analysis.name}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Generate Button */}
                <button className="w-full btn btn-primary btn-sm mt-4">
                  Generate Analysis
                </button>
              </div>
            </Accordion.Content>
          </Accordion.Item>

          {/* 5. Watchlist & Alerts */}
          <Accordion.Item value="watchlist" className="border-b border-border">
            <Accordion.Trigger className="flex h-12 w-full items-center justify-between px-4 py-2 text-left text-sm font-medium hover:bg-accent transition-colors [&[data-state=open]>svg]:rotate-180">
              <div className="flex items-center space-x-2">
                <Eye size={16} className="text-purple-500" />
                <span>Watchlist & Alerts</span>
              </div>
              <ChevronDown size={16} className="transition-transform duration-200" />
            </Accordion.Trigger>
            <Accordion.Content className="overflow-hidden data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down">
              <div className="p-4 space-y-3">
                {/* Watchlist */}
                <div>
                  <label className="text-xs font-medium text-muted-foreground">My Watchlist</label>
                  <div className="mt-2 space-y-2">
                    {['BTCUSDT', 'ETHUSDT', 'ADAUSDT'].map(symbol => (
                      <div key={symbol} className="flex items-center justify-between p-2 bg-accent rounded-md">
                        <span className="text-xs font-medium">{symbol}</span>
                        <button className="text-xs text-destructive hover:text-destructive/80">
                          Remove
                        </button>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Price Alerts */}
                <div>
                  <label className="text-xs font-medium text-muted-foreground">Price Alerts</label>
                  <div className="mt-2 space-y-2">
                    <div className="flex space-x-2">
                      <input 
                        type="number" 
                        placeholder="Price"
                        className="input flex-1"
                      />
                      <select className="input">
                        <option>Above</option>
                        <option>Below</option>
                      </select>
                    </div>
                    <button className="w-full btn btn-outline btn-sm">
                      Add Alert
                    </button>
                  </div>
                </div>
              </div>
            </Accordion.Content>
          </Accordion.Item>
        </Accordion.Root>
      </div>
    </div>
  )
}

export default Sidebar
