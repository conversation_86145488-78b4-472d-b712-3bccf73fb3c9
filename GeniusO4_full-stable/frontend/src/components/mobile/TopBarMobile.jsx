import React from 'react'
import { 
  BarChart3, 
  User, 
  Zap, 
  Activity,
  Wifi,
  WifiOff
} from 'lucide-react'
import { cn } from '../../lib/utils'

/**
 * Mobile Top Bar for Telegram WebApp
 * Compact design optimized for mobile screens
 */
export const TopBarMobile = ({ 
  user, 
  theme, 
  isTelegramWebApp,
  connectionStatus = 'connected' 
}) => {
  return (
    <div className="h-full px-4 flex items-center justify-between">
      {/* Left Section - Logo */}
      <div className="flex items-center space-x-2">
        <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
          <BarChart3 size={18} className="text-primary-foreground" />
        </div>
        <div>
          <h1 className="text-base font-bold text-foreground">ChartGenius</h1>
          <p className="text-xs text-muted-foreground leading-none">v2.0</p>
        </div>
      </div>

      {/* Center Section - Status Indicators */}
      <div className="flex items-center space-x-2">
        {/* AI Status */}
        <div className="flex items-center space-x-1 px-2 py-1 bg-green-100 dark:bg-green-900/20 rounded-full">
          <Zap size={10} className="text-green-600 dark:text-green-400" />
          <span className="text-xs text-green-700 dark:text-green-300 font-medium">AI</span>
        </div>

        {/* Connection Status */}
        <div className={cn(
          "flex items-center space-x-1 px-2 py-1 rounded-full",
          connectionStatus === 'connected' 
            ? "bg-blue-100 dark:bg-blue-900/20" 
            : "bg-red-100 dark:bg-red-900/20"
        )}>
          {connectionStatus === 'connected' ? (
            <>
              <Activity size={10} className="text-blue-600 dark:text-blue-400" />
              <span className="text-xs text-blue-700 dark:text-blue-300 font-medium">Live</span>
            </>
          ) : (
            <>
              <WifiOff size={10} className="text-red-600 dark:text-red-400" />
              <span className="text-xs text-red-700 dark:text-red-300 font-medium">Offline</span>
            </>
          )}
        </div>
      </div>

      {/* Right Section - User */}
      <div className="flex items-center">
        {user ? (
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
              {user.photo_url ? (
                <img 
                  src={user.photo_url} 
                  alt={user.first_name} 
                  className="w-8 h-8 rounded-full object-cover"
                />
              ) : (
                <User size={14} className="text-primary-foreground" />
              )}
            </div>
            <div className="hidden sm:block">
              <p className="text-sm font-medium text-foreground leading-none">
                {user.first_name}
              </p>
              <p className="text-xs text-muted-foreground leading-none">
                {user.username ? `@${user.username}` : 'User'}
              </p>
            </div>
          </div>
        ) : (
          <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center">
            <User size={14} className="text-muted-foreground" />
          </div>
        )}
      </div>
    </div>
  )
}

export default TopBarMobile
