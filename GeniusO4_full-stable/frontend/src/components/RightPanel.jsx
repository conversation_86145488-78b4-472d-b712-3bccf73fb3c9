import React, { useState } from 'react'
import {
  ChevronLeft,
  ChevronRight,
  TrendingUp,
  BarChart3,
  Activity,
  Info,
  Shield
} from 'lucide-react'
import { cn } from '../lib/utils'
import TradingRecommendationsWidget from './TradingRecommendationsWidget'
import KeyIndicatorsWidget from './KeyIndicatorsWidget'
import SupportResistanceWidget from './SupportResistanceWidget'

/**
 * Right Panel component for ChartGenius v2.0
 * Contains trading recommendations, quick metrics, and analysis results
 * Supports collapsible behavior and responsive design
 */
export const RightPanel = ({
  collapsed = false,
  onToggle,
  children,
  rawApiResponse = null, // Новый пропс для реальных данных
  recommendations = null,
  isLoading = false,
  className = '',
  ...props
}) => {
  const [activeSection, setActiveSection] = useState('recommendations')

  // Panel sections configuration
  const sections = [
    {
      id: 'recommendations',
      title: 'Рекомендации',
      icon: TrendingUp,
      priority: 'high'
    },
    {
      id: 'indicators',
      title: 'Индикаторы',
      icon: BarChart3,
      priority: 'high'
    },
    {
      id: 'levels',
      title: 'Уровни',
      icon: Shield,
      priority: 'medium'
    },
    {
      id: 'analysis',
      title: 'Анализ',
      icon: Activity,
      priority: 'medium'
    },
    {
      id: 'info',
      title: 'Информация',
      icon: Info,
      priority: 'low'
    }
  ]

  if (collapsed) {
    return (
      <div 
        className={cn(
          'pro-panel-compact flex flex-col items-center space-y-3 transition-all duration-300',
          'w-12 min-h-[400px] border-l border-pro-border',
          className
        )}
        {...props}
      >
        {/* Expand button */}
        <button
          onClick={onToggle}
          className="p-2 rounded-md hover:bg-pro-button-hover transition-colors"
          title="Развернуть панель"
        >
          <ChevronLeft size={16} className="text-pro-text-secondary" />
        </button>

        {/* Collapsed section icons */}
        <div className="flex flex-col space-y-2">
          {sections.map(section => {
            const Icon = section.icon
            return (
              <div 
                key={section.id}
                className={cn(
                  'p-2 rounded-md transition-colors cursor-pointer',
                  activeSection === section.id 
                    ? 'bg-pro-focus/20 text-pro-focus' 
                    : 'text-pro-text-muted hover:text-pro-text-secondary'
                )}
                title={section.title}
                onClick={() => setActiveSection(section.id)}
              >
                <Icon size={16} />
              </div>
            )
          })}
        </div>
      </div>
    )
  }

  return (
    <div 
      className={cn(
        'pro-panel flex flex-col transition-all duration-300',
        'w-80 min-h-[400px] border-l border-pro-border',
        className
      )}
      {...props}
    >
      {/* Panel Header */}
      <div className="flex items-center justify-between mb-4 pb-3 border-b border-pro-border-light">
        <h3 className="text-sm font-semibold text-pro-text-primary">
          Анализ и рекомендации
        </h3>
        
        {/* Collapse button */}
        <button
          onClick={onToggle}
          className="p-1.5 rounded-md hover:bg-pro-button-hover transition-colors"
          title="Свернуть панель"
        >
          <ChevronRight size={16} className="text-pro-text-secondary" />
        </button>
      </div>

      {/* Panel Content */}
      <div className="flex-1 overflow-y-auto space-y-4">
        {/* Trading Recommendations Widget */}
        <TradingRecommendationsWidget
          recommendations={recommendations}
          rawApiResponse={rawApiResponse}
          isLoading={isLoading}
        />

        {/* Key Indicators Widget */}
        <KeyIndicatorsWidget
          rawApiResponse={rawApiResponse}
          isLoading={isLoading}
        />

        {/* Support/Resistance Widget */}
        <SupportResistanceWidget
          rawApiResponse={rawApiResponse}
          isLoading={isLoading}
        />

        {/* Additional children content */}
        {children}
      </div>
    </div>
  )
}

/**
 * Right Panel Section component
 * Wrapper for individual sections within the right panel
 */
export const RightPanelSection = ({ 
  title, 
  icon: Icon, 
  children, 
  collapsible = true,
  defaultOpen = true,
  priority = 'medium',
  className = '',
  ...props 
}) => {
  const [isOpen, setIsOpen] = useState(defaultOpen)

  const priorityStyles = {
    high: 'border-l-4 border-l-pro-success',
    medium: 'border-l-4 border-l-pro-info',
    low: 'border-l-4 border-l-pro-text-muted'
  }

  return (
    <div 
      className={cn(
        'pro-panel-compact',
        priorityStyles[priority],
        className
      )}
      {...props}
    >
      {/* Section Header */}
      <div 
        className={cn(
          'flex items-center justify-between',
          collapsible ? 'cursor-pointer' : '',
          'mb-3'
        )}
        onClick={collapsible ? () => setIsOpen(!isOpen) : undefined}
      >
        <div className="flex items-center space-x-2">
          {Icon && <Icon size={16} className="text-pro-text-secondary" />}
          <h4 className="text-sm font-medium text-pro-text-primary">
            {title}
          </h4>
        </div>
        
        {collapsible && (
          <ChevronRight 
            size={14} 
            className={cn(
              'text-pro-text-muted transition-transform duration-200',
              isOpen ? 'rotate-90' : ''
            )}
          />
        )}
      </div>

      {/* Section Content */}
      {(!collapsible || isOpen) && (
        <div className="space-y-2">
          {children}
        </div>
      )}
    </div>
  )
}

export default RightPanel
