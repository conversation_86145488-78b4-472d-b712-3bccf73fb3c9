import React, { useState, useEffect } from 'react'
import {
  TrendingUp,
  TrendingDown,
  Minus,
  Target,
  Shield,
  AlertTriangle,
  Clock,
  DollarSign
} from 'lucide-react'
import { cn } from '../lib/utils'
import { parseAnalysisData, extractTradingRecommendations, formatPrice, formatDate } from '../utils/analysisDataParser'
import { CollapsiblePanelCompact } from './CollapsiblePanel'

/**
 * Trading Recommendations Widget for ChartGenius v2.0
 * Compact component for displaying trading recommendations in the right panel
 * Features color coding, animations, and priority-based display
 */
export const TradingRecommendationsWidget = ({
  recommendations = null,
  rawApiResponse = null, // Новый пропс для реальных данных от API
  isLoading = false,
  lastUpdated = null,
  className = '',
  ...props
}) => {
  const [animateUpdate, setAnimateUpdate] = useState(false)

  // Trigger animation when recommendations update
  useEffect(() => {
    if (recommendations && !isLoading) {
      setAnimateUpdate(true)
      const timer = setTimeout(() => setAnimateUpdate(false), 600)
      return () => clearTimeout(timer)
    }
  }, [recommendations, isLoading])

  // Extract primary recommendation - handle multiple data structures
  const getPrimaryRecommendation = () => {
    // Priority 1: Real API data from JSON file
    if (rawApiResponse) {
      const analysisData = parseAnalysisData(rawApiResponse)
      if (analysisData) {
        const tradingRecs = extractTradingRecommendations(analysisData)
        if (tradingRecs.length > 0) {
          const primary = tradingRecs[0]
          return {
            strategy: primary.strategy,
            risk: primary.risk,
            stop_loss: primary.stopLoss,
            take_profit: primary.takeProfit,
            entry_price: primary.entryPrice,
            entry_date: primary.entryDate,
            profit: primary.profit,
            details: primary.details,
            signal_type: primary.signal // BUY, SELL, HOLD
          }
        }
      }
    }

    // Priority 2: Backend trading_strategies structure
    if (recommendations?.trading_strategies?.length) {
      return recommendations.trading_strategies[0]
    }

    // Priority 3: Demo recommendations structure
    if (recommendations?.recommendations) {
      return {
        strategy: recommendations.recommendations.action === 'BUY' ? 'Рекомендуется покупка' :
                 recommendations.recommendations.action === 'SELL' ? 'Рекомендуется продажа' :
                 'Удержание позиции',
        risk: recommendations.risk_assessment?.level || 'medium',
        stop_loss: recommendations.recommendations.stop_loss,
        take_profit: recommendations.recommendations.target_price,
        confidence: recommendations.recommendations.confidence
      }
    }

    return null
  }

  // Get recommendation type and styling
  const getRecommendationType = (strategy) => {
    if (!strategy) return { type: 'neutral', color: 'pro-text-muted', icon: Minus }

    // Check signal_type first (from real API data)
    if (strategy.signal_type) {
      switch (strategy.signal_type.toUpperCase()) {
        case 'BUY':
          return { type: 'buy', color: 'pro-success', icon: TrendingUp }
        case 'SELL':
          return { type: 'sell', color: 'pro-danger', icon: TrendingDown }
        case 'HOLD':
        default:
          return { type: 'hold', color: 'pro-warning', icon: Minus }
      }
    }

    // Fallback to text analysis
    if (!strategy?.strategy) return { type: 'neutral', color: 'pro-text-muted', icon: Minus }

    const text = strategy.strategy.toLowerCase()

    if (text.includes('покуп') || text.includes('long') || text.includes('восходящ')) {
      return { type: 'buy', color: 'pro-success', icon: TrendingUp }
    }

    if (text.includes('продаж') || text.includes('short') || text.includes('нисходящ')) {
      return { type: 'sell', color: 'pro-danger', icon: TrendingDown }
    }

    return { type: 'hold', color: 'pro-warning', icon: Minus }
  }

  // Get risk level styling
  const getRiskStyling = (risk) => {
    if (!risk) return { color: 'pro-text-muted', label: 'Не определен' }
    
    const riskText = risk.toLowerCase()
    
    if (riskText.includes('высок') || riskText.includes('high')) {
      return { color: 'pro-danger', label: 'Высокий' }
    }
    
    if (riskText.includes('средн') || riskText.includes('умерен') || riskText.includes('medium')) {
      return { color: 'pro-warning', label: 'Средний' }
    }
    
    if (riskText.includes('низк') || riskText.includes('low')) {
      return { color: 'pro-success', label: 'Низкий' }
    }
    
    return { color: 'pro-text-muted', label: risk }
  }

  const primaryRec = getPrimaryRecommendation()
  const recType = primaryRec ? getRecommendationType(primaryRec) : null
  const riskStyling = primaryRec ? getRiskStyling(primaryRec.risk) : null

  if (isLoading) {
    return (
      <div 
        className={cn(
          'pro-panel-compact border-l-4 border-l-pro-info',
          className
        )}
        {...props}
      >
        <div className="flex items-center space-x-2 mb-3">
          <Target size={16} className="text-pro-info animate-pulse" />
          <h4 className="text-sm font-medium text-pro-text-primary">
            Торговые рекомендации
          </h4>
        </div>
        
        <div className="space-y-2">
          <div className="h-4 bg-pro-bg-tertiary rounded animate-pulse"></div>
          <div className="h-3 bg-pro-bg-tertiary rounded w-3/4 animate-pulse"></div>
          <div className="h-3 bg-pro-bg-tertiary rounded w-1/2 animate-pulse"></div>
        </div>
      </div>
    )
  }

  if (!primaryRec) {
    return (
      <div 
        className={cn(
          'pro-panel-compact border-l-4 border-l-pro-text-muted',
          className
        )}
        {...props}
      >
        <div className="flex items-center space-x-2 mb-3">
          <Target size={16} className="text-pro-text-muted" />
          <h4 className="text-sm font-medium text-pro-text-primary">
            Торговые рекомендации
          </h4>
        </div>
        
        <div className="text-xs text-pro-text-secondary">
          Рекомендации отсутствуют. Запустите анализ для получения торговых сигналов.
        </div>
      </div>
    )
  }

  const RecommendationIcon = recType.icon

  return (
    <CollapsiblePanelCompact
      title="Торговые рекомендации"
      icon={Target}
      badge={recType.type === 'buy' ? 'BUY' : recType.type === 'sell' ? 'SELL' : 'HOLD'}
      defaultOpen={true}
      persistKey="trading-recommendations"
      className={cn(
        'border-l-4 transition-all duration-300',
        `border-l-${recType.color}`,
        animateUpdate ? 'animate-pulse-glow' : '',
        className
      )}
      {...props}
    >

      {/* Primary Recommendation */}
      <div className="space-y-3">
        {/* Main Signal */}
        <div className="flex items-center space-x-2">
          <RecommendationIcon 
            size={18} 
            className={cn(
              `text-${recType.color}`,
              animateUpdate ? 'animate-bounce' : ''
            )} 
          />
          <div className="flex-1">
            <div className={`text-sm font-semibold text-${recType.color} uppercase`}>
              {recType.type === 'buy' ? 'ПОКУПКА' : 
               recType.type === 'sell' ? 'ПРОДАЖА' : 'УДЕРЖАНИЕ'}
            </div>
            <div className="text-xs text-pro-text-secondary line-clamp-2">
              {primaryRec.strategy}
            </div>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-2 gap-2 text-xs">
          {/* Stop Loss */}
          {primaryRec.stop_loss && (
            <div className="flex items-center space-x-1">
              <Shield size={12} className="text-pro-danger" />
              <span className="text-pro-text-muted">SL:</span>
              <span className="text-pro-danger font-medium">
                {formatPrice(primaryRec.stop_loss)}
              </span>
            </div>
          )}

          {/* Take Profit */}
          {primaryRec.take_profit && (
            <div className="flex items-center space-x-1">
              <DollarSign size={12} className="text-pro-success" />
              <span className="text-pro-text-muted">TP:</span>
              <span className="text-pro-success font-medium">
                {formatPrice(primaryRec.take_profit)}
              </span>
            </div>
          )}

          {/* Entry Price (for real API data) */}
          {primaryRec.entry_price && (
            <div className="flex items-center space-x-1">
              <DollarSign size={12} className="text-pro-info" />
              <span className="text-pro-text-muted">Вход:</span>
              <span className="text-pro-info font-medium">
                {formatPrice(primaryRec.entry_price)}
              </span>
            </div>
          )}
        </div>

        {/* Risk Level */}
        {riskStyling && (
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-1">
              <AlertTriangle size={12} className={`text-${riskStyling.color}`} />
              <span className="text-xs text-pro-text-muted">Риск:</span>
            </div>
            <span className={`text-xs font-medium text-${riskStyling.color}`}>
              {riskStyling.label}
            </span>
          </div>
        )}

        {/* Profit Potential (for real API data) */}
        {primaryRec?.profit && (
          <div className="flex items-center justify-between pt-2 border-t border-pro-border-light">
            <span className="text-xs text-pro-text-muted">Потенциал:</span>
            <span className="text-xs font-medium text-pro-success">
              {primaryRec.profit}
            </span>
          </div>
        )}

        {/* Entry Date (for real API data) */}
        {primaryRec?.entry_date && (
          <div className="flex items-center justify-between">
            <span className="text-xs text-pro-text-muted">Дата входа:</span>
            <span className="text-xs font-medium text-pro-text-primary">
              {formatDate(primaryRec.entry_date)}
            </span>
          </div>
        )}

        {/* Additional Strategies Indicator */}
        {recommendations?.trading_strategies?.length > 1 && (
          <div className="text-xs text-pro-text-muted text-center pt-2 border-t border-pro-border-light">
            +{recommendations.trading_strategies.length - 1} дополнительных стратегий
          </div>
        )}

        {/* Confidence Level for demo data */}
        {primaryRec?.confidence && (
          <div className="flex items-center justify-between pt-2 border-t border-pro-border-light">
            <span className="text-xs text-pro-text-muted">Уверенность:</span>
            <span className="text-xs font-medium text-pro-info">
              {Math.round(primaryRec.confidence * 100)}%
            </span>
          </div>
        )}
      </div>
    </CollapsiblePanelCompact>
  )
}

export default TradingRecommendationsWidget
