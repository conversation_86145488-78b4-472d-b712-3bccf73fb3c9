import React from 'react'
import { cn } from '../../lib/utils'

/**
 * Watermark component for ChartGenius v2.0
 * Displays the 371.png watermark with different opacity levels
 */
export const Watermark = ({ 
  type = 'chart', 
  className = '', 
  children,
  ...props 
}) => {
  const getWatermarkClass = (type) => {
    switch (type) {
      case 'chart':
        return 'watermark-chart' // 10% opacity
      case 'summary':
        return 'watermark-summary' // 4% opacity
      case 'explanations':
        return 'watermark-explanations' // 3% opacity
      case 'export':
        return 'watermark-export' // 8% opacity
      default:
        return 'watermark-chart'
    }
  }

  return (
    <div 
      className={cn(
        'relative',
        className
      )}
      {...props}
    >
      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
      
      {/* Watermark overlay */}
      <div 
        className={cn(
          'absolute inset-0 z-0',
          getWatermarkClass(type)
        )}
        aria-hidden="true"
      />
    </div>
  )
}

/**
 * Chart watermark wrapper
 */
export const ChartWatermark = ({ children, className, ...props }) => (
  <Watermark 
    type="chart" 
    className={cn('min-h-[400px]', className)}
    {...props}
  >
    {children}
  </Watermark>
)

/**
 * Summary watermark wrapper
 */
export const SummaryWatermark = ({ children, className, ...props }) => (
  <Watermark 
    type="summary" 
    className={cn('p-4', className)}
    {...props}
  >
    {children}
  </Watermark>
)

/**
 * Explanations watermark wrapper
 */
export const ExplanationsWatermark = ({ children, className, ...props }) => (
  <Watermark 
    type="explanations" 
    className={cn('p-4', className)}
    {...props}
  >
    {children}
  </Watermark>
)

/**
 * Export watermark wrapper (for PDF/PNG exports)
 */
export const ExportWatermark = ({ children, className, ...props }) => (
  <Watermark 
    type="export" 
    className={className}
    {...props}
  >
    {children}
  </Watermark>
)

export default Watermark
