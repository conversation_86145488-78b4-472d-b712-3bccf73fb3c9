import React, { useState } from 'react'
import { ChevronDown, ChevronUp } from 'lucide-react'
import { cn } from '../lib/utils'

/**
 * Universal Collapsible Panel Component for ChartGenius v2.0
 * Provides space optimization with smooth animations and state persistence
 * 
 * Features:
 * - Smooth expand/collapse animations
 * - Customizable default state
 * - Professional styling consistent with trading platform theme
 * - Accessible keyboard navigation
 * - Optional state persistence via localStorage
 */
export const CollapsiblePanel = ({
  title,
  children,
  defaultOpen = true,
  className = '',
  headerClassName = '',
  contentClassName = '',
  persistKey = null, // Key for localStorage persistence
  icon: Icon = null,
  badge = null,
  onToggle = null,
  disabled = false,
  ...props
}) => {
  // Initialize state with persistence support
  const getInitialState = () => {
    if (persistKey && typeof window !== 'undefined') {
      const saved = localStorage.getItem(`collapsible-${persistKey}`)
      return saved !== null ? JSON.parse(saved) : defaultOpen
    }
    return defaultOpen
  }

  const [isOpen, setIsOpen] = useState(getInitialState)

  const handleToggle = () => {
    if (disabled) return
    
    const newState = !isOpen
    setIsOpen(newState)
    
    // Persist state if key provided
    if (persistKey && typeof window !== 'undefined') {
      localStorage.setItem(`collapsible-${persistKey}`, JSON.stringify(newState))
    }
    
    // Call external toggle handler
    if (onToggle) {
      onToggle(newState)
    }
  }

  return (
    <div 
      className={cn(
        'pro-panel overflow-hidden transition-all duration-300 ease-in-out',
        className
      )}
      {...props}
    >
      {/* Header */}
      <button
        onClick={handleToggle}
        disabled={disabled}
        className={cn(
          'flex items-center justify-between w-full text-left',
          'hover:bg-pro-button-hover transition-colors duration-150',
          'focus:outline-none focus:ring-2 focus:ring-pro-focus focus:ring-opacity-50',
          'disabled:opacity-50 disabled:cursor-not-allowed',
          'p-2 -m-2 rounded-md',
          headerClassName
        )}
        aria-expanded={isOpen}
        aria-controls={`collapsible-content-${persistKey || 'default'}`}
      >
        <div className="flex items-center space-x-3 min-w-0 flex-1">
          {/* Icon */}
          {Icon && (
            <Icon 
              size={16} 
              className="text-pro-text-secondary flex-shrink-0" 
            />
          )}
          
          {/* Title */}
          <h3 className="text-sm font-medium text-pro-text-primary truncate">
            {title}
          </h3>
          
          {/* Badge */}
          {badge && (
            <span className="pro-status pro-status-info text-xs flex-shrink-0">
              {badge}
            </span>
          )}
        </div>
        
        {/* Collapse/Expand Icon */}
        <div className="flex-shrink-0 ml-2">
          {isOpen ? (
            <ChevronUp 
              size={16} 
              className="text-pro-text-muted transition-transform duration-200" 
            />
          ) : (
            <ChevronDown 
              size={16} 
              className="text-pro-text-muted transition-transform duration-200" 
            />
          )}
        </div>
      </button>

      {/* Content */}
      <div
        id={`collapsible-content-${persistKey || 'default'}`}
        className={cn(
          'transition-all duration-300 ease-in-out overflow-hidden',
          isOpen ? 'max-h-screen opacity-100 mt-3' : 'max-h-0 opacity-0 mt-0'
        )}
      >
        <div className={cn('space-y-2', contentClassName)}>
          {children}
        </div>
      </div>
    </div>
  )
}

/**
 * Compact version for right panel widgets
 */
export const CollapsiblePanelCompact = ({
  title,
  children,
  defaultOpen = true,
  className = '',
  icon: Icon = null,
  badge = null,
  persistKey = null,
  onToggle = null,
  ...props
}) => {
  const getInitialState = () => {
    if (persistKey && typeof window !== 'undefined') {
      const saved = localStorage.getItem(`collapsible-compact-${persistKey}`)
      return saved !== null ? JSON.parse(saved) : defaultOpen
    }
    return defaultOpen
  }

  const [isOpen, setIsOpen] = useState(getInitialState)

  const handleToggle = () => {
    const newState = !isOpen
    setIsOpen(newState)
    
    if (persistKey && typeof window !== 'undefined') {
      localStorage.setItem(`collapsible-compact-${persistKey}`, JSON.stringify(newState))
    }
    
    if (onToggle) {
      onToggle(newState)
    }
  }

  return (
    <div 
      className={cn(
        'pro-panel-compact transition-all duration-300 ease-in-out',
        className
      )}
      {...props}
    >
      {/* Compact Header */}
      <button
        onClick={handleToggle}
        className="flex items-center justify-between w-full text-left hover:bg-pro-button-hover transition-colors duration-150 p-1 -m-1 rounded"
        aria-expanded={isOpen}
      >
        <div className="flex items-center space-x-2 min-w-0 flex-1">
          {Icon && (
            <Icon 
              size={14} 
              className="text-pro-text-secondary flex-shrink-0" 
            />
          )}
          <h4 className="text-xs font-medium text-pro-text-primary truncate">
            {title}
          </h4>
          {badge && (
            <span className="text-xs text-pro-text-muted flex-shrink-0">
              {badge}
            </span>
          )}
        </div>
        
        <div className="flex-shrink-0 ml-1">
          {isOpen ? (
            <ChevronUp size={12} className="text-pro-text-muted" />
          ) : (
            <ChevronDown size={12} className="text-pro-text-muted" />
          )}
        </div>
      </button>

      {/* Compact Content */}
      <div
        className={cn(
          'transition-all duration-300 ease-in-out overflow-hidden',
          isOpen ? 'max-h-screen opacity-100 mt-2' : 'max-h-0 opacity-0 mt-0'
        )}
      >
        {children}
      </div>
    </div>
  )
}

export default CollapsiblePanel
