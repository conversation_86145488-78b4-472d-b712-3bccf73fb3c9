import React from 'react'
import {
  ArrowUp,
  ArrowDown,
  Shield,
  Target,
  TrendingUp,
  TrendingDown
} from 'lucide-react'
import { cn } from '../lib/utils'
import { parseAnalysisData, extractSupportResistanceLevels, formatPrice } from '../utils/analysisDataParser'
import { CollapsiblePanelCompact } from './CollapsiblePanel'

/**
 * Support & Resistance Widget for ChartGenius v2.0
 * Displays key support and resistance levels from real analysis data
 */
export const SupportResistanceWidget = ({ 
  rawApiResponse = null,
  isLoading = false,
  className = '',
  ...props 
}) => {
  // Extract support/resistance levels from real API data
  const getLevels = () => {
    if (!rawApiResponse) return null
    
    const analysisData = parseAnalysisData(rawApiResponse)
    if (!analysisData) return null
    
    return extractSupportResistanceLevels(analysisData)
  }

  const levels = getLevels()

  if (isLoading) {
    return (
      <div 
        className={cn(
          'pro-panel-compact border-l-4 border-l-pro-info',
          className
        )}
        {...props}
      >
        <div className="flex items-center space-x-2 mb-3">
          <Shield size={16} className="text-pro-info animate-pulse" />
          <h4 className="text-sm font-medium text-pro-text-primary">
            Поддержка/Сопротивление
          </h4>
        </div>
        
        <div className="space-y-2">
          <div className="h-4 bg-pro-bg-tertiary rounded animate-pulse"></div>
          <div className="h-3 bg-pro-bg-tertiary rounded w-3/4 animate-pulse"></div>
          <div className="h-3 bg-pro-bg-tertiary rounded w-1/2 animate-pulse"></div>
        </div>
      </div>
    )
  }

  if (!levels || (!levels.supports?.length && !levels.resistances?.length)) {
    return (
      <div 
        className={cn(
          'pro-panel-compact border-l-4 border-l-pro-text-muted',
          className
        )}
        {...props}
      >
        <div className="flex items-center space-x-2 mb-3">
          <Shield size={16} className="text-pro-text-muted" />
          <h4 className="text-sm font-medium text-pro-text-primary">
            Поддержка/Сопротивление
          </h4>
        </div>
        
        <div className="text-xs text-pro-text-secondary">
          Уровни поддержки и сопротивления не определены
        </div>
      </div>
    )
  }

  return (
    <CollapsiblePanelCompact
      title="Поддержка/Сопротивление"
      icon={Shield}
      badge={`${levels.supports?.length || 0}/${levels.resistances?.length || 0}`}
      defaultOpen={true}
      persistKey="support-resistance"
      className={cn(
        'border-l-4 border-l-pro-info',
        className
      )}
      {...props}
    >

      <div className="space-y-3">
        {/* Resistance Levels */}
        {levels.resistances?.length > 0 && (
          <div>
            <div className="flex items-center space-x-2 mb-2">
              <ArrowUp size={12} className="text-pro-danger" />
              <span className="text-xs font-medium text-pro-danger">Сопротивление</span>
            </div>
            <div className="space-y-1">
              {levels.resistances.slice(0, 3).map((level, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-1">
                    <TrendingUp size={10} className="text-pro-danger" />
                    <span className="text-xs text-pro-text-muted">R{index + 1}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-xs font-semibold text-pro-danger">
                      {formatPrice(level.price)}
                    </div>
                    {level.explanation && (
                      <div className="text-xs text-pro-text-muted max-w-24 truncate">
                        {level.explanation}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Support Levels */}
        {levels.supports?.length > 0 && (
          <div>
            <div className="flex items-center space-x-2 mb-2">
              <ArrowDown size={12} className="text-pro-success" />
              <span className="text-xs font-medium text-pro-success">Поддержка</span>
            </div>
            <div className="space-y-1">
              {levels.supports.slice(0, 3).map((level, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-1">
                    <TrendingDown size={10} className="text-pro-success" />
                    <span className="text-xs text-pro-text-muted">S{index + 1}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-xs font-semibold text-pro-success">
                      {formatPrice(level.price)}
                    </div>
                    {level.explanation && (
                      <div className="text-xs text-pro-text-muted max-w-24 truncate">
                        {level.explanation}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Additional Info */}
      <div className="mt-3 pt-2 border-t border-pro-border-light">
        <div className="text-xs text-pro-text-muted text-center">
          {levels.resistances?.length > 3 || levels.supports?.length > 3 ? 
            `+${(levels.resistances?.length || 0) + (levels.supports?.length || 0) - 6} дополнительных уровней` :
            'Ключевые уровни'
          }
        </div>
      </div>
    </CollapsiblePanelCompact>
  )
}

export default SupportResistanceWidget
