import React from 'react';
import { Eye, EyeOff, TrendingUp, Target, Layers, Activity } from 'lucide-react';

/**
 * Компонент управления аналитическими наложениями на график
 */
export default function OverlayControls({ overlaySettings, onSettingsChange }) {
  const toggleOverlay = (overlayType) => {
    onSettingsChange({
      ...overlaySettings,
      [overlayType]: !overlaySettings[overlayType]
    });
  };

  const overlayTypes = [
    {
      key: 'showSupportResistance',
      label: 'Поддержка/Сопротивление',
      icon: Target,
      color: 'text-green-400',
      description: 'Горизонтальные уровни поддержки и сопротивления'
    },
    {
      key: 'showTrendLines',
      label: 'Трендовые линии',
      icon: TrendingUp,
      color: 'text-blue-400',
      description: 'Восходящие и нисходящие трендовые линии'
    },
    {
      key: 'showFibonacci',
      label: 'Уровни Фибоначчи',
      icon: Layers,
      color: 'text-yellow-400',
      description: 'Локальные и глобальные уровни коррекции Фибоначчи'
    },
    {
      key: 'showImbalances',
      label: 'Зоны дисбаланса',
      icon: Activity,
      color: 'text-orange-400',
      description: 'Fair Value Gap, Single Print, Vector Candle'
    }
  ];

  return (
    <div className="bg-gray-800 rounded-lg p-4 space-y-3">
      <h3 className="text-sm font-medium text-gray-200 mb-3 flex items-center gap-2">
        <Layers className="w-4 h-4" />
        Аналитические наложения
      </h3>
      
      {overlayTypes.map(({ key, label, icon: Icon, color, description }) => (
        <div key={key} className="flex items-center justify-between group">
          <div className="flex items-center gap-3 flex-1">
            <Icon className={`w-4 h-4 ${color}`} />
            <div className="flex-1">
              <div className="text-sm text-gray-200">{label}</div>
              <div className="text-xs text-gray-400">{description}</div>
            </div>
          </div>
          
          <button
            onClick={() => toggleOverlay(key)}
            className={`p-1 rounded transition-colors ${
              overlaySettings[key]
                ? 'text-green-400 hover:text-green-300'
                : 'text-gray-500 hover:text-gray-400'
            }`}
            title={overlaySettings[key] ? 'Скрыть' : 'Показать'}
          >
            {overlaySettings[key] ? (
              <Eye className="w-4 h-4" />
            ) : (
              <EyeOff className="w-4 h-4" />
            )}
          </button>
        </div>
      ))}
      
      <div className="pt-2 border-t border-gray-700">
        <div className="flex gap-2">
          <button
            onClick={() => onSettingsChange({
              showSupportResistance: true,
              showTrendLines: true,
              showFibonacci: true,
              showImbalances: true
            })}
            className="flex-1 px-3 py-1 text-xs bg-green-600 hover:bg-green-700 text-white rounded transition-colors"
          >
            Показать все
          </button>
          <button
            onClick={() => onSettingsChange({
              showSupportResistance: false,
              showTrendLines: false,
              showFibonacci: false,
              showImbalances: false
            })}
            className="flex-1 px-3 py-1 text-xs bg-gray-600 hover:bg-gray-700 text-white rounded transition-colors"
          >
            Скрыть все
          </button>
        </div>
      </div>
    </div>
  );
}
