import { useState, useEffect } from 'react'
import { getTelegramTheme, isTelegramWebApp } from '../lib/utils'

/**
 * Custom hook for managing dark mode
 * Supports both manual toggle and Telegram WebApp theme detection
 */
export function useDarkMode() {
  const [isDark, setIsDark] = useState(() => {
    // Check if running in Telegram WebApp
    if (isTelegramWebApp()) {
      return getTelegramTheme() === 'dark'
    }
    
    // Check localStorage first
    const stored = localStorage.getItem('theme')
    if (stored) {
      return stored === 'dark'
    }
    
    // Fall back to system preference
    return window.matchMedia('(prefers-color-scheme: dark)').matches
  })

  useEffect(() => {
    const root = window.document.documentElement
    
    if (isDark) {
      root.classList.add('dark')
    } else {
      root.classList.remove('dark')
    }
    
    // Save to localStorage (unless in Telegram WebApp)
    if (!isTelegramWebApp()) {
      localStorage.setItem('theme', isDark ? 'dark' : 'light')
    }
  }, [isDark])

  useEffect(() => {
    // Listen for Telegram WebApp theme changes
    if (isTelegramWebApp()) {
      const handleThemeChange = () => {
        const telegramTheme = getTelegramTheme()
        setIsDark(telegramTheme === 'dark')
      }

      // Listen for theme changes in Telegram
      window.Telegram?.WebApp?.onEvent('themeChanged', handleThemeChange)
      
      return () => {
        window.Telegram?.WebApp?.offEvent('themeChanged', handleThemeChange)
      }
    } else {
      // Listen for system theme changes
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      
      const handleSystemThemeChange = (e) => {
        // Only update if user hasn't manually set a preference
        const stored = localStorage.getItem('theme')
        if (!stored) {
          setIsDark(e.matches)
        }
      }

      mediaQuery.addEventListener('change', handleSystemThemeChange)
      
      return () => {
        mediaQuery.removeEventListener('change', handleSystemThemeChange)
      }
    }
  }, [])

  const toggle = () => {
    // Don't allow manual toggle in Telegram WebApp
    if (!isTelegramWebApp()) {
      setIsDark(prev => !prev)
    }
  }

  const setTheme = (theme) => {
    // Don't allow manual theme setting in Telegram WebApp
    if (!isTelegramWebApp()) {
      setIsDark(theme === 'dark')
    }
  }

  return {
    isDark,
    toggle,
    setTheme,
    theme: isDark ? 'dark' : 'light',
    canToggle: !isTelegramWebApp() // Can only toggle outside of Telegram
  }
}

export default useDarkMode
