import React from 'react'

/**
 * Watchlist Page component
 * User's tracked symbols and alerts
 */
export const WatchlistPage = () => {
  return (
    <div className="w-full h-full p-4">
      <div className="space-y-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-foreground">Watchlist</h1>
          <p className="text-muted-foreground">Your tracked symbols and alerts</p>
        </div>
        
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">My Watchlist</h3>
          </div>
          <div className="card-content">
            <p className="text-muted-foreground">Watchlist content will be displayed here...</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default WatchlistPage
