import React, { useState, useEffect } from 'react'
import { ChartWatermark } from '../components/ui/Watermark'
import { cn } from '../lib/utils'

/**
 * Chart Page component
 * Main trading chart with watermark overlay
 */
export const ChartPage = () => {
  const [isLoading, setIsLoading] = useState(true)
  const [chartData, setChartData] = useState(null)

  // Simulate chart data loading
  useEffect(() => {
    const loadChartData = async () => {
      setIsLoading(true)
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock chart data
      setChartData({
        symbol: 'BTCUSDT',
        timeframe: '4h',
        lastUpdate: new Date().toISOString()
      })
      
      setIsLoading(false)
    }

    loadChartData()
  }, [])

  if (isLoading) {
    return (
      <ChartWatermark className="w-full h-full flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto" />
          <p className="text-muted-foreground">Loading chart data...</p>
        </div>
      </ChartWatermark>
    )
  }

  return (
    <ChartWatermark className="w-full h-full">
      <div className="h-full flex flex-col">
        {/* Chart Header */}
        <div className="flex-shrink-0 p-4 border-b border-border">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold text-foreground">
                {chartData?.symbol || 'BTCUSDT'}
              </h2>
              <p className="text-sm text-muted-foreground">
                {chartData?.timeframe || '4h'} • Last updated: {new Date().toLocaleTimeString()}
              </p>
            </div>
            
            <div className="flex items-center space-x-2">
              <button className="btn btn-outline btn-sm">
                Indicators
              </button>
              <button className="btn btn-primary btn-sm">
                Analyze
              </button>
            </div>
          </div>
        </div>

        {/* Chart Container */}
        <div className="flex-1 relative">
          {/* Placeholder for TradingView chart or custom chart component */}
          <div className="absolute inset-0 flex items-center justify-center bg-muted/10 rounded-lg m-4">
            <div className="text-center space-y-4">
              <div className="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto">
                <svg 
                  className="w-8 h-8 text-primary" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" 
                  />
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-medium text-foreground">Trading Chart</h3>
                <p className="text-sm text-muted-foreground">
                  Interactive chart will be displayed here
                </p>
                <p className="text-xs text-muted-foreground mt-2">
                  TradingView widget or custom chart component
                </p>
              </div>
            </div>
          </div>

          {/* Chart Controls Overlay */}
          <div className="absolute bottom-4 left-4 right-4 flex justify-between items-end">
            {/* Timeframe Selector */}
            <div className="flex items-center space-x-1 bg-card/80 backdrop-blur rounded-lg p-1 border border-border">
              {['1m', '5m', '15m', '1h', '4h', '1d'].map((tf) => (
                <button
                  key={tf}
                  className={cn(
                    "px-3 py-1 text-xs font-medium rounded-md transition-colors",
                    tf === '4h' 
                      ? "bg-primary text-primary-foreground" 
                      : "text-muted-foreground hover:text-foreground hover:bg-accent"
                  )}
                >
                  {tf}
                </button>
              ))}
            </div>

            {/* Chart Tools */}
            <div className="flex items-center space-x-2">
              <button className="p-2 bg-card/80 backdrop-blur rounded-lg border border-border hover:bg-accent transition-colors">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                </svg>
              </button>
              
              <button className="p-2 bg-card/80 backdrop-blur rounded-lg border border-border hover:bg-accent transition-colors">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </button>
              
              <button className="p-2 bg-card/80 backdrop-blur rounded-lg border border-border hover:bg-accent transition-colors">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </ChartWatermark>
  )
}

export default ChartPage
