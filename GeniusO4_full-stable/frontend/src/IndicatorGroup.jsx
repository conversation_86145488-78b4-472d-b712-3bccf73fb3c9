import React from 'react';

export default function IndicatorGroup({ title, indicators, layers, toggleLayer }) {
  if (!indicators.length) return null;
  return (
    <div className="mb-4">
      <h4 className="text-sm font-medium text-gray-700 mb-2">{title}</h4>
      <div className="space-y-2">
        {indicators.map((ind) => (
          <label key={ind} className="flex items-center">
            <input
              type="checkbox"
              checked={layers.includes(ind)}
              onChange={() => toggleLayer(ind)}
              className="mr-2"
            />
            <span className="text-sm">{ind}</span>
          </label>
        ))}
      </div>
    </div>
  );
}
