import React, { useState } from 'react'
import { cn } from '../lib/utils'
import { TopBar } from '../components/TopBar'
import { Sidebar } from '../components/Sidebar'
import { RightPane } from '../components/RightPane'

/**
 * Desktop Layout for ChartGenius v2.0
 * Layout: TopBar + Sidebar (12%) + Chart (60%) + Right-Pane (28%)
 */
export const DesktopLayout = ({ children }) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [rightPaneCollapsed, setRightPaneCollapsed] = useState(false)

  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Top Bar */}
      <TopBar 
        onSidebarToggle={() => setSidebarCollapsed(!sidebarCollapsed)}
        onRightPaneToggle={() => setRightPaneCollapsed(!rightPaneCollapsed)}
        sidebarCollapsed={sidebarCollapsed}
        rightPaneCollapsed={rightPaneCollapsed}
      />
      
      {/* Main Content Area */}
      <div className="flex-1 flex overflow-hidden">
        {/* Sidebar - 12% width when expanded */}
        <div 
          className={cn(
            "transition-all duration-300 ease-in-out border-r border-border bg-card",
            sidebarCollapsed ? "w-16" : "w-[12%] min-w-[280px]"
          )}
        >
          <Sidebar 
            collapsed={sidebarCollapsed}
            onToggle={() => setSidebarCollapsed(!sidebarCollapsed)}
          />
        </div>

        {/* Chart Area - 60% width (flexible) */}
        <div 
          className={cn(
            "flex-1 transition-all duration-300 ease-in-out",
            sidebarCollapsed && rightPaneCollapsed ? "w-full" :
            sidebarCollapsed ? "w-[72%]" :
            rightPaneCollapsed ? "w-[88%]" :
            "w-[60%]"
          )}
        >
          <div className="h-full p-4">
            {children}
          </div>
        </div>

        {/* Right Pane - 28% width when expanded */}
        <div 
          className={cn(
            "transition-all duration-300 ease-in-out border-l border-border bg-card",
            rightPaneCollapsed ? "w-16" : "w-[28%] min-w-[320px]"
          )}
        >
          <RightPane 
            collapsed={rightPaneCollapsed}
            onToggle={() => setRightPaneCollapsed(!rightPaneCollapsed)}
          />
        </div>
      </div>
    </div>
  )
}

export default DesktopLayout
