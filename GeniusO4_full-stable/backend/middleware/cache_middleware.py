# backend/middleware/cache_middleware.py

import os
import json
import hashlib
import logging
from typing import Callable, Any
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from services.redis_client import get_redis_client

logger = logging.getLogger(__name__)

class CacheMiddleware(BaseHTTPMiddleware):
    """
    Middleware для кэширования HTTP ответов в Redis
    """
    
    def __init__(self, app, cache_enabled: bool = None, default_ttl: int = 900):
        super().__init__(app)
        self.cache_enabled = cache_enabled if cache_enabled is not None else os.getenv("ENABLE_CACHE", "false").lower() == "true"
        self.default_ttl = default_ttl
        
        # Пути, которые можно кэшировать
        self.cacheable_paths = {
            "/api/analysis/ohlcv",  # OHLCV данные
            "/api/analysis/indicators",  # Технические индикаторы
        }
        
        # Пути, которые НИКОГДА не кэшируются
        self.non_cacheable_paths = {
            "/api/analysis/llm",  # LLM ответы
            "/api/auth",  # Аутентификация
            "/api/admin",  # Админ функции
            "/api/config",  # Конфигурация
        }
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Основная логика middleware"""
        
        # Проверяем, нужно ли кэшировать этот запрос
        if not self._should_cache_request(request):
            return await call_next(request)
        
        # Генерируем ключ кэша
        cache_key = self._generate_cache_key(request)
        
        # Пытаемся получить из кэша
        cached_response = self._get_cached_response(cache_key)
        if cached_response:
            logger.debug(f"Ответ получен из кэша: {cache_key}")
            return self._create_response_from_cache(cached_response)
        
        # Выполняем запрос
        response = await call_next(request)
        
        # Кэшируем ответ если он успешный
        if response.status_code == 200:
            await self._cache_response(cache_key, response)
        
        return response
    
    def _should_cache_request(self, request: Request) -> bool:
        """
        Определяет, нужно ли кэшировать запрос
        
        Args:
            request: HTTP запрос
            
        Returns:
            True если запрос можно кэшировать
        """
        # Кэш отключен
        if not self.cache_enabled:
            return False
        
        # Кэшируем только GET запросы
        if request.method != "GET":
            return False
        
        path = request.url.path
        
        # Проверяем запрещенные пути
        for non_cacheable_path in self.non_cacheable_paths:
            if path.startswith(non_cacheable_path):
                return False
        
        # Проверяем разрешенные пути
        for cacheable_path in self.cacheable_paths:
            if path.startswith(cacheable_path):
                return True
        
        return False
    
    def _generate_cache_key(self, request: Request) -> str:
        """
        Генерирует ключ кэша для запроса
        
        Args:
            request: HTTP запрос
            
        Returns:
            Ключ кэша
        """
        # Создаем строку из пути и параметров запроса
        path = request.url.path
        query_params = str(request.query_params)
        
        # Добавляем заголовки, которые могут влиять на ответ
        relevant_headers = {}
        for header_name in ["authorization", "accept-language"]:
            if header_name in request.headers:
                relevant_headers[header_name] = request.headers[header_name]
        
        # Создаем уникальную строку
        cache_string = f"{path}?{query_params}&headers={json.dumps(relevant_headers, sort_keys=True)}"
        
        # Хэшируем для получения короткого ключа
        cache_hash = hashlib.md5(cache_string.encode()).hexdigest()
        
        return f"http_cache:{cache_hash}"
    
    def _get_cached_response(self, cache_key: str) -> dict:
        """
        Получает кэшированный ответ
        
        Args:
            cache_key: Ключ кэша
            
        Returns:
            Кэшированный ответ или None
        """
        try:
            return redis_client.get(cache_key)
        except Exception as e:
            logger.error(f"Ошибка получения из кэша {cache_key}: {e}")
            return None
    
    async def _cache_response(self, cache_key: str, response: Response) -> bool:
        """
        Кэширует HTTP ответ
        
        Args:
            cache_key: Ключ кэша
            response: HTTP ответ
            
        Returns:
            True если успешно кэшировано
        """
        try:
            # Читаем тело ответа
            response_body = b""
            async for chunk in response.body_iterator:
                response_body += chunk
            
            # Создаем новый итератор для ответа
            response.body_iterator = self._create_body_iterator(response_body)
            
            # Подготавливаем данные для кэширования
            cache_data = {
                "status_code": response.status_code,
                "headers": dict(response.headers),
                "body": response_body.decode("utf-8"),
                "media_type": response.media_type
            }
            
            # Определяем TTL в зависимости от типа данных
            ttl = self._get_ttl_for_path(cache_key)
            
            # Сохраняем в кэш
            redis_client = get_redis_client()
            success = redis_client.set(cache_key, cache_data, ttl) if redis_client else False
            
            if success:
                logger.debug(f"Ответ кэширован: {cache_key} (TTL: {ttl}s)")
            
            return success
            
        except Exception as e:
            logger.error(f"Ошибка кэширования ответа {cache_key}: {e}")
            return False
    
    def _create_body_iterator(self, body: bytes):
        """Создает итератор для тела ответа"""
        async def body_iterator():
            yield body
        return body_iterator()
    
    def _create_response_from_cache(self, cached_data: dict) -> Response:
        """
        Создает HTTP ответ из кэшированных данных
        
        Args:
            cached_data: Кэшированные данные
            
        Returns:
            HTTP ответ
        """
        response = Response(
            content=cached_data["body"],
            status_code=cached_data["status_code"],
            headers=cached_data["headers"],
            media_type=cached_data.get("media_type")
        )
        
        # Добавляем заголовок, указывающий что ответ из кэша
        response.headers["X-Cache"] = "HIT"
        
        return response
    
    def _get_ttl_for_path(self, cache_key: str) -> int:
        """
        Определяет TTL для кэша в зависимости от типа данных
        
        Args:
            cache_key: Ключ кэша
            
        Returns:
            TTL в секундах
        """
        # OHLCV данные кэшируем на 15 минут
        if "ohlcv" in cache_key:
            return int(os.getenv("REDIS_TTL_OHLCV", "900"))
        
        # Индикаторы кэшируем на 10 минут
        if "indicators" in cache_key:
            return 600
        
        # По умолчанию
        return self.default_ttl

class CacheControl:
    """
    Утилиты для управления кэшем
    """
    
    @staticmethod
    def clear_cache_pattern(pattern: str) -> int:
        """
        Очищает кэш по паттерну
        
        Args:
            pattern: Паттерн для поиска ключей
            
        Returns:
            Количество удаленных ключей
        """
        try:
            keys = redis_client.get_keys_pattern(pattern)
            deleted_count = 0
            
            for key in keys:
                if redis_client.delete(key):
                    deleted_count += 1
            
            logger.info(f"Очищено {deleted_count} ключей по паттерну {pattern}")
            return deleted_count
            
        except Exception as e:
            logger.error(f"Ошибка очистки кэша по паттерну {pattern}: {e}")
            return 0
    
    @staticmethod
    def clear_ohlcv_cache() -> int:
        """Очищает кэш OHLCV данных"""
        return CacheControl.clear_cache_pattern("http_cache:*ohlcv*")
    
    @staticmethod
    def clear_all_http_cache() -> int:
        """Очищает весь HTTP кэш"""
        return CacheControl.clear_cache_pattern("http_cache:*")
    
    @staticmethod
    def get_cache_stats() -> dict:
        """
        Получает статистику кэша
        
        Returns:
            Словарь со статистикой
        """
        try:
            http_cache_keys = redis_client.get_keys_pattern("http_cache:*")
            
            stats = {
                "total_http_cache_keys": len(http_cache_keys),
                "cache_enabled": os.getenv("ENABLE_CACHE", "false").lower() == "true",
                "redis_connected": redis_client.is_connected()
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"Ошибка получения статистики кэша: {e}")
            return {"error": str(e)}
