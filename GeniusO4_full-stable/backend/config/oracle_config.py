# backend/config/oracle_config.py

import os
import logging
from typing import Optional, Dict, Any
import oracledb
from dotenv import load_dotenv

load_dotenv()

logger = logging.getLogger(__name__)

class OracleConfig:
    """
    Конфигурация для Oracle Autonomous JSON Database
    """
    
    def __init__(self):
        # Oracle AJD Connection parameters
        self.username = os.getenv("ORACLE_USERNAME")
        self.password = os.getenv("ORACLE_PASSWORD") 
        self.dsn = os.getenv("ORACLE_DSN")  # Connection string for AJD
        self.wallet_location = os.getenv("ORACLE_WALLET_LOCATION")
        self.wallet_password = os.getenv("ORACLE_WALLET_PASSWORD")
        
        # Connection pool settings
        self.pool_min = int(os.getenv("ORACLE_POOL_MIN", "2"))
        self.pool_max = int(os.getenv("ORACLE_POOL_MAX", "10"))
        self.pool_increment = int(os.getenv("ORACLE_POOL_INCREMENT", "1"))
        
        # Validate required parameters
        self._validate_config()
    
    def _validate_config(self):
        """Валидация конфигурации Oracle"""
        required_params = ["username", "password", "dsn"]
        missing_params = []
        
        for param in required_params:
            if not getattr(self, param):
                missing_params.append(f"ORACLE_{param.upper()}")
        
        if missing_params:
            raise ValueError(f"Отсутствуют обязательные параметры Oracle: {', '.join(missing_params)}")
    
    def get_connection_params(self) -> Dict[str, Any]:
        """Получить параметры подключения"""
        params = {
            "user": self.username,
            "password": self.password,
            "dsn": self.dsn,
            # Для Oracle AJD требуется SSL
            "ssl_context": True,
            "ssl_server_cert_dn": None  # Отключаем проверку сертификата для упрощения
        }

        # Добавляем wallet только если указан реальный путь
        if self.wallet_location and self.wallet_location != "/path/to/wallet":
            params["config_dir"] = self.wallet_location
            if self.wallet_password and self.wallet_password != "your_wallet_password":
                params["wallet_password"] = self.wallet_password

        return params
    
    def get_pool_params(self) -> Dict[str, Any]:
        """Получить параметры пула соединений"""
        params = self.get_connection_params()
        params.update({
            "min": self.pool_min,
            "max": self.pool_max,
            "increment": self.pool_increment
        })
        return params

# Глобальная конфигурация
oracle_config = OracleConfig()
