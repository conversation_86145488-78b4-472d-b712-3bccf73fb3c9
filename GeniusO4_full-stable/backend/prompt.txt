{{ ohlc_data | tojson | default([]) }}

Тебе переданы данные в формате JSON о свечах и значениях индикаторов, соответствующих каждой свече.
Пожалуйста, проанализируй все данные о свечах и индикаторах, начиная с самой первой свечи и заканчивая последней доступной свечой. 
Используйте все данные в диапазоне от первой до последней свечи. 
Верни подробный анализ на русском в формате JSON. 
Следуй ВСЕМ правилам биржевого технического анализа. 
Используй только предоставленные данные и не генерируй вымышленных значений. 
Убедись, что JSON валиден, без использования блоков кода, со всеми строками и ключами в двойных кавычках, а числовые значения представлены как числа.

Удели **МНОГО** внимания точности ответа.
Убедись перед отправкой, что ты проверил соответствие всех своих выводов предоставленным данным и инструкциям. 
Главный приоритет - это **максимальная точность анализа**, поэтому можешь потратить в 10 раз больше времени на тщательную проверку ответа и полученных данных. 
Можешь не ограничивать себя в формулировках, лимит токенов на ответ достаточно большой - более 14000.

Заполни следующие разделы:

1. **"primary_analysis"**: широкая оценка актива, согласно ВСЕМ правилам биржевого технического анализа с учётом индикаторов, аргументируй и подтверди свой вывод.
2. **"confidence_in_trading_decisions"**: Оценка уверенности в торговых решениях, обоснование.
3. **"support_resistance_levels"**: определи уровни поддержки и сопротивления, аргументируй.
   - **Критерии:**
     - Уровни должны быть протестированы (касались цены) минимум несколько раз, чтобы подтвердить их значимость.     
     - Уровни должны совпадать с важными индикаторами, такими как уровни Фибоначчи, пивотные точки или скользящие средние, что добавляет дополнительную значимость.
     - Наличие сильных ценовых баров (например, большие свечи с длинными тенями) на этих уровнях, что может указывать на активность покупателей или продавцов.
     - Убедись, что предложенные уровни соответствуют ценам свечей, на которые ты ссылаешься, и что они были достигнуты в процессе анализа.
   - **Методы:**
     - Используй лучи, начинающиеся с экстремальной точки первой свечи и направленные вправо, чтобы визуализировать уровни поддержки и сопротивления.
     - Подтверждай уровни с помощью объёмов: увеличение объёмов при касании уровня может указывать на его значимость и подтверждать его как уровень поддержки или сопротивления.
     - При анализе уровней учитывай контекст рынка и текущие тренды, чтобы определить, являются ли уровни актуальными для текущей ситуации.
4. **"trend_lines"**: найди и укажи начальные и конечные точки тренда (даты и цены). локальные и глобальные тренды.
5. **"pivot_points"**: Анализ пивотных точек. для учета в анализе.
6. **"unfinished_zones"**: Определи и опиши зоны: Bad High/Low, Weak High/Low, Poor High/Low, Sweep Levels. укажи если не выявлено.
7. **"imbalances"**: Выяви и опиши Single Prints, Fair Value Gaps, Vector Candles.укажи если не выявлено.
8. **"fibonacci_analysis"**: Проведи анализ уровней Фибоначчи, согласно правилам построения "от прошлой даты к будущей, от минимума цены до максимума, либо наоборот, в зависимости от текущего тренда". согласно актуальным трендам(локальным/глобальным), которые ты определил. аргументируй.
    - **Привязка fibonacci к хвостам свечей:** Начальная и конечная точки уровней Фибоначчи должны быть привязаны к "High" и "Low" ценам свечей.
    - **Автоматизированная идентификация:** Используй индикаторы, такие как MACD и ADX, для подтверждения силы тренда перед выбором точек Фибоначчи.
9. **"elliott_wave_analysis"**: Используй предоставленные данные для идентификации текущих волн и прогнозирования следующих. Следуй правилам волнового анализа Эллиота, учитывая соотношения между длинами волн и временными интервалами. сделай предположение о дальнейших волнах с конкреными точками для визуализации(дата,цена). помни какие волны не могут быть выше других
10. **"divergence_analysis"**: Определи наличие дивергенций RSI, СМВ и прочие если найдешь (бычья/медвежья дивергенция, скрытые, обычные, расширенные) и их значение. аргументируй и укажи о чем свидетельствует.
11. **"structural_edge"**: найди, выяви и опиши Swing Fail и Failed Auction. укажи если не выявлено
12. **Идентификация паттернов и аномалий:**: 
   - Ищи классические свечные паттерны (например, поглощение, молот, падающая звезда, прочие) и графические паттерны (например, голова и плечи, треугольники, прочие).
   - Определи аномалии, такие как резкие скачки объёма или необычные движения цены, которые могут указывать на события или изменения в настроениях рынка.
13. **"indicators_analysis"**: Детальный анализ ключевых индикаторов (RSI, MACD, объём, OBV, Williams %R, Parabolic SAR, Ichimoku Cloud, VWAP, Moving Average Envelopes и другие).
14. **"volatility_analysis"**: Оценка текущей волатильности для понимания уровня риска.
15. **"volume_analysis"**: Проведи детальный анализ объёмов торгов, включая изменения объёмов при достижении ключевых уровней поддержки и сопротивления, а также соотношение объёма с движением цены.
16. **"indicator_correlations"**: Исследуй корреляции между различными техническими индикаторами, а также анализируй связь между ATR и уровнем волатильности.
17. **"gap_analysis"**: Выяви и проанализируй Gaps, оцени их влияние на рыночные настроения и тренды. аргументируй.
18. **"psychological_levels"**: Определи психологически значимые уровни поддержки и сопротивления (круглые числа, уровни с большими объёмами), а также свободные зоны (Fair Value Gaps), которые могут служить уровнями для будущих движений цены. аргументируй.
19. **"extended_ichimoku_analysis"**: Проведи расширенный анализ облаков Ichimoku, включая пересечения линий Conversion Line и Base Line, а также оцени положение цены относительно облака для подтверждения тренда.
20. **"anomalous_candles": Выяви аномальные свечи с необычными размерами тел или теней и проанализируй последовательности свечей для прогнозирования смены тренда.
21. **"market_cycles_identification"**: Идентификация текущего рыночного цикла и его влияние на прогнозирование.
22. **"price_prediction"**: Гипотеза о движении цены в следующие 24 часа с данными для визуализации (виртуальные свечи). Это единственный раздел, где ты можешь генерировать виртуальные данные и предполагать реальное движение цены с ростом и коррекциями. Количество виртуальных свечей должно соответствовать количеству свечей, необходимых для прогнозирования движения цены в следующие 24 часа, в зависимости от таймфрейма (например, 24 свечи для часового таймфрейма, 6 свечей для 4-часового и т.д.). Виртуальные свечи должны быть расположены в будущем относительно последней свечи, виртуальные свечи должны включать как падения, так и подъемы, чтобы отразить типичное поведение рынка. Убедитесь, что все данные, используемые для создания виртуальных свечей, соответствуют проведенному анализу и рекомендациям.
    **Требования**
                -Проверь, что даты виртуальных свечей находятся в будущем относительно даты последней свечи.-Убедись, что виртуальные свечи создаются на основе анализа текущих трендов и уровней поддержки и сопротивления.
                -Симулируй рыночное движение, которое может быть неравномерным, с коррекциями и импульсами, чтобы отразить реальную динамику рынка.
                - При создании виртуальных свечей необходимо использовать только последовательные временные метки.
23. **"risk_management"**: Выявление потенциальных рисков и рекомендации по их снижению.
24. **"explanations"**: Объяснения ко всем разделам анализа.
25. **"recommendations"**: Рекомендации по торговым стратегиям с деталями (стратегия, short/long, точка входа, стоп, риск, прибыль и прочее). Каждая стратегия должна содержать цену входа, цену выхода, уровень стоп-лосса и тейк-профита, а также обоснование выбора этих уровней. Этот раздел должен коррелироваться со всем анализом и особенно с 'price_prediction'. Убедитесь, что все рекомендации основаны на предыдущем анализе и поддерживаются данными из раздела 'price_prediction'.
    **Требования**
                - Убедись, что даты в рекомендациях являются будущими датами относительно даты последней свечи.
                - Проверь, что каждая стратегия имеет четкую связь с анализом, включая уровни поддержки и сопротивления, а также прогнозируемое движение цены.
                - Обоснуй каждую стратегию, ссылаясь на данные из анализа, чтобы обеспечить согласованность и логичность.
26. **"feedback"**: ты абсолютно уверен в своих выводах? почему? Замечания для внимания и улучшения текущего промпта. укажи свои Model configuration: Temperature, Top P, Frequency penalty, Presence penalty. Какой временной период ты проанализировал? какие данные ты пропустил и не использовал в анализе? почему? с какими проблемами столкнулся: в ходе формирования ответа? заполнения раздела с данными для визуализации?  инструкции промпта тебе были понятны? ответь подробно, как улучшить этот промпт, повысить для тебя понятность промпта, инструкций, определений и точность анализа?

**Важно:**
- Заполняя каждый раздел используй **только предоставленные данные** или делай обоснованные предположения на их основе.
- Избегай придумывать или генерировать данные, не основанные на исходной информации.
- Избегай использовать круглые числа.
- Обеспечь полные данные для визуализации: Все данные ("price","date"), необходимые для отрисовки элементов (уровни Фибоначчи, уровни поддержки и сопротивления, паттерны, волны Эллиота, трендовые линии и индикаторы), должны быть включены в соответствующие разделы.
- При построении уровней Фибоначчи используй **точные экстремумы** (High и Low) из предоставленных данных. Убедись, что начальная и конечная точки уровней Фибоначчи соответствуют реальным максимумам и минимумам свечей.
- Проводи **точные расчёты** для всех параметров.
- Убедись, что все данные, необходимые для визуализации, включены в соответствующие разделы с правильной структурой.
- Убедись, что числовые значения в JSON представлены без разделителей тысяч и десятичных запятых. Используй точку в качестве десятичного разделителя, а разделители тысяч не используй.
- Убедитесь, что анализ охватывает все ключевые аспекты технического анализа, включая тренды, уровни поддержки и сопротивления, объемы, волатильность и другие индикаторы.
- Все даты должны быть в формате 'YYYY-MM-DD HH:MM:SS', где часы должны быть от 00 до 23.
- Включить проверку на корректность формата даты и времени перед генерацией виртуальных свечей, чтобы гарантировать, что все временные метки соответствуют реальному времени.

Структура каждого раздела, где требуется предоставление данных для визуализации:

{
    "primary_analysis": {
        "global_trend": "Описание глобального тренда на основе предоставленных данных и индикаторов.",
        "local_trend": "Описание локального тренда за более короткий период.",
        "patterns": "Описание обнаруженных паттернов в ценовом движении.",
        "anomalies": "Описание выявленных аномалий или необычных событий."
    },
    "confidence_in_trading_decisions": {
        "confidence": "confidence_type",
        "reason": "Обоснование уровня уверенности в торговых решениях."
    },
    "unfinished_zones": [
        {
            "type": "zone_type_1",
            "level": level_price_number_1,
            "date": "YYYY-MM-DD HH:MM:SS",
            "line_style": "solid", // Или "dashed", "dotted"
            "line_color": "color_description",
            "explanation": "Объяснение данной незавершённой зоны."
        }
        // Добавьте минимум 3, максимум n зон
    ],
    "imbalances": [
        {
            "type": "imbalance_type_1",
            "start_point": {
                "date": "YYYY-MM-DD HH:MM:SS",
                "price": start_price_number_1
            },
            "end_point": {
                "date": "YYYY-MM-DD HH:MM:SS",
                "price": end_price_number_1
            },
            "price_range": [low_price_number_1, high_price_number_1],
            "explanation": "Объяснение данного дисбаланса."
        }
        // Добавьте минимум 3, максимум n дисбалансов
    ],
    "support_resistance_levels": {
    "supports": [
        {
            "level": price_number,
            "date": "YYYY-MM-DD HH:MM:SS",
            "explanation": "Почему данный уровень считается поддержкой.",
            "ray_slope": "угол наклона луча, если требуется" // Опционально
        }
        // Добавьте минимум 3, максимум n уровней
    ],
    "resistances": [
            {
               "level": price_number,
               "date": "YYYY-MM-DD HH:MM:SS",
               "explanation": "Почему данный уровень считается сопротивлением.",
               "ray_slope": "угол наклона луча, если требуется" // Опционально
            }
            // Добавьте минимум 3, максимум n уровней
        ]
    },
    "trend_lines": {
        "lines": [
            {
                "type": "восходящая или нисходящая",
                "start_point": {
                    "date": "YYYY-MM-DD HH:MM:SS",
                    "price": start_price_number_1
                },
                "end_point": {
                    "date": "YYYY-MM-DD HH:MM:SS",
                    "price": end_price_number_1
                },
                "slope_angle": "угол наклона линии" // Опционально
            }
            // Добавьте необходимые трендовые линии минимум 3, максимум n линий 
        ]
    },
        "fair_value_gaps": [
            {
                "date": "YYYY-MM-DD HH:MM:SS",
                "price_range": [low_price_number, high_price_number],
                "explanation": "Описание свободной зоны и её потенциального влияния."
            }
            // Добавьте минимум 1, максимум n линий
        ],
    "fibonacci_analysis": {
        "based_on_local_trend": {
            "levels": {
                "0%": level_value_number_0_local,
                "23.6%": level_value_number_23_6_local,
                "50%": level_value_number_50_local,
                "61.8%": level_value_number_61_8_local,
                "75%": level_value_number_75_local,
                "86,6%": level_value_number_86_6_local,
                "100%": level_value_number_100_local
            },
            "start_point": {
                "date": "YYYY-MM-DD HH:MM:SS",
                "price": start_price_number_local
            },
            "end_point": {
                "date": "YYYY-MM-DD HH:MM:SS",
                "price": end_price_number_local
            },
            "explanation": "Объяснение уровней Фибоначчи на основе локального тренда. аргументы. проверка соответствия цен start_pointи end_point данным свечи. Проверка соответствия правилам построения"
        },
        "based_on_global_trend": {
            "levels": {
                "0%": level_value_number_0_global,
                "23.6%": level_value_number_23_6_global,
                "50%": level_value_number_50_global,
                "61.8%": level_value_number_61_8_global,
                "75%": level_value_number_75_global,
                "86,6%": level_value_number_86_6_global,
                "100%": level_value_number_100_global
            },
            "start_point": {
                "date": "YYYY-MM-DD HH:MM:SS",
                "price": start_price_number_global
            },
            "end_point": {
                "date": "YYYY-MM-DD HH:MM:SS",
                "price": end_price_number_global
            },
            "explanation": "Объяснение уровней Фибоначчи на основе локального тренда. аргументы. проверка соответствия цен start_pointи end_point данным свечи. Проверка соответствия правилам построения"
        }
    },
    "elliott_wave_analysis": {
        "current_wave": "Описание текущей волны Эллиота.",
        "wave_count": wave_count_number,
        "forecast": "Прогноз на основе волн Эллиота. Проверка соответствия правилам построения",
        "waves": [
            {
                "wave_number": wave_number_integer_1,
                "start_point": {
                    "date": "YYYY-MM-DD HH:MM:SS",
                    "price": start_price_number_1
                },
                "end_point": {
                    "date": "YYYY-MM-DD HH:MM:SS",
                    "price": end_price_number_1
                }
            }
            // Добавьте волны при необходимости (минимум 3, максимум 7)
        ],
        "explanation": "Объяснение анализа волн Эллиота. аргументы. Проверка соответствия правилам построения"
    },
    "divergence_analysis": [
        {
            "indicator": "RSI",
            "type": "bullish_or_bearish_divergence",
            "date": "YYYY-MM-DD HH:MM:SS",
            "explanation": "Объяснение дивергенции и её значимости. аргументы."
        }
        // Добавьте минимум 3, максимум 7 дивергенций
    ],
    "structural_edge": [
        {
            "type": "Swing Fail",
            "date": "YYYY-MM-DD HH:MM:SS",
            "price": price_number_1,
            "explanation": "Описание и значение данного явления. аргументы."
        }
        // Добавьте дополнительные элементы при необходимости (максимум до 7)
    ],
    "candlestick_patterns": [
        {
            "date": "YYYY-MM-DD HH:MM:SS",
            "type": "pattern_type_1",
            "price":price_number_1,
            "explanation": "Краткое объяснение значимости паттерна. аргументы."
        }
        // Добавьте минимум 3, максимум 7 паттернов
    ],
    "indicators_analysis": {
        "RSI": {
            "current_value": rsi_value,
            "trend": "trend_description",
            "comment": "Комментарий по RSI."
        },
        "MACD": {
            "current_value": macd_value,
            "signal": macd_signal_value,
            "histogram": macd_histogram_value,
            "trend": "trend_description",
            "comment": "Комментарий по MACD."
        },
        "OBV": {
            "current_value": obv_value,
            "trend": "trend_description",
            "comment": "Комментарий по OBV."
        },
        "ATR": {
            "current_value": atr_value,
            "trend": "trend_description",
            "comment": "Комментарий по ATR."
        },
        "Stochastic_Oscillator": {
            "current_value": stochastic_value,
            "trend": "trend_description",
            "comment": "Комментарий по стохастическому осцилятору."
        },
        "Bollinger_Bands": {
            "upper_band": bollinger_upper_value,
            "middle_band": bollinger_middle_value,
            "lower_band": bollinger_lower_value,
            "trend": "trend_description",
            "comment": "Комментарий по полосам Боллинджера."
        },
        "Ichimoku_Cloud": {
            "ichimoku_a": ichimoku_a_value,
            "ichimoku_b": ichimoku_b_value,
            "base_line": ichimoku_base_line_value,
            "conversion_line": ichimoku_conversion_line_value,
            "trend": "trend_description",
            "comment": "Комментарий по облакам Ichimoku."
        },
        "ADX": {
            "current_value": adx_value,
            "trend": "trend_description",
            "comment": "Комментарий по ADX."
        },
        "Parabolic_SAR": {
            "current_value": parabolic_sar_value,
            "trend": "trend_description",
            "comment": "Комментарий по Parabolic SAR."
        },
        "VWAP": {
            "current_value": vwap_value,
            "trend": "trend_description",
            "comment": "Комментарий по VWAP."
        },
        "Moving_Average_Envelopes": {
            "upper_envelope": upper_envelope_value,
            "lower_envelope": lower_envelope_value,
            "trend": "trend_description",
            "comment": "Комментарий по Moving Average Envelopes."
        }
        // Добавьте другие индикаторы по необходимости или предложите в рекомендациях
    },
    "volume_analysis": {
        "volume_trends": "Анализ изменений объёмов при достижении ключевых уровней и соотношения объёма с движением цены.",
        "significant_volume_changes": [
            {
                "date": "YYYY-MM-DD HH:MM:SS",
                "price": price_number,
                "volume": volume_number,
                "explanation": "Описание значительного изменения объёма и его влияния."
            }
            // Добавьте минимум 3, максимум 7 записей
        ]
    },
    "indicator_correlations": {
        "macd_rsi_correlation": "Анализ корреляции между MACD и RSI.",
        "atr_volatility_correlation": "Анализ связи между ATR и уровнем волатильности.",
        "explanation": "Объяснение значимости выявленных корреляций. Проверка соответствия правильности выводов"
    },
    "gap_analysis": {
        "gaps": [
            {
                "date": "YYYY-MM-DD HH:MM:SS",
                "gap_type": "Common Gap, Breakaway Gap, Runaway Gap или Exhaustion Gap",
                "price_range": [low_price_number, high_price_number],
                "explanation": "Описание пробела и его влияние на рынок."
            }
            // Добавьте минимум 1, максимум 5 пробелов
        ],
        "comment": "Общий комментарий по анализу пробелов. Проверка соответствия правилам определения"
    },
    "psychological_levels": {
        "levels": [
            {
                "level": price_number,
                "date": "YYYY-MM-DD HH:MM:SS",
                "type": "Support или Resistance",
                "explanation": "Почему данный уровень считается психологическим."
            }
            // Добавьте минимум 3, максимум 7 уровней
        ]
    },
    "fair_value_gaps": [
            {
                "date": "YYYY-MM-DD HH:MM:SS",
                "price_range": [low_price_number, high_price_number],
                "explanation": "Описание свободной зоны и её потенциального влияния. Проверка соответствия правилам определения"
            }
            // Добавьте минимум 1, максимум 5 записей
        ],
    "extended_ichimoku_analysis": {
        "conversion_base_line_cross": {
            "date": "YYYY-MM-DD HH:MM:SS",
            "signal": "Bullish Cross или Bearish Cross",
            "explanation": "Описание пересечения линий и его значимости."
        },
        "price_vs_cloud": {
            "position": "Above, Below или Within the Cloud",
            "explanation": "Положение цены относительно облака и его значение."
        },
        "comment": "Общий комментарий по расширенному анализу Ichimoku."
    },
    "volatility_by_intervals": {
        "morning_volatility": {
            "average_volatility": volatility_value_morning,
            "comment": "Комментарий по волатильности в утренние часы."
        },
        "evening_volatility": {
            "average_volatility": volatility_value_evening,
            "comment": "Комментарий по волатильности в вечерние часы."
        },
        "comparison": "Сравнение волатильности между разными временными интервалами."
    },
    "anomalous_candles": [
        {
            "date": "YYYY-MM-DD HH:MM:SS",
            "type": "Anomalous Candle Type",
            "price": price_number,
            "explanation": "Описание аномалии и её возможного влияния."
        }
        // Добавьте минимум 3, максимум 7 аномальных свечей
    },
    "price_prediction": {
        "forecast": "Описание прогнозируемого движения цены в следующие 24 часа. аргументы. Проверка соответствия правилам построения!",
        "virtual_candles": [
            {
                "date": "YYYY-MM-DD HH:MM:SS",
                "open": open_price_number_1,
                "high": high_price_number_1,
                "low": low_price_number_1,
                "close": close_price_number_1
            }
            // Добавьте необходимое количество виртуальных свечей
        ]
    },
    "recommendations": {
        "trading_strategies": [
            {
                "strategy": "Описание торговой стратегии.",
                "entry_point": {
                    "Price": entry_price_number_1,
                    "Date": "YYYY-MM-DD HH:MM:SS"
                },
                "exit_point": {
                    "Price": exit_price_number_1,
                    "Date": "YYYY-MM-DD HH:MM:SS"
                },
                "stop_loss": stop_loss_price_number_1,
                "take_profit": take_profit_price_number_1,
                "risk": "Описание уровня риска.",
                "profit": "Описание потенциальной прибыли.",
                "other_details": "Дополнительные детали о стратегии. аргументы. Проверка соответствия правилам построения"
            }
            // Добавьте минимум 4, максимум n стратегий
        ]
    },
    "feedback": {
        "note": "Любые дополнительные замечания или области, требующие внимания. проверка выводов анализа."
    }
}

Перед отправкой анализа проверьте соответствие всех упоминаемых цен фактическим данным, а так же соответствие ответа **ВСЕМ** инструкциям.
