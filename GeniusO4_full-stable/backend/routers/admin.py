# backend/routers/admin.py

import os
from datetime import datetime, timedelta
from typing import Dict, Any, List
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from config.config import logger
from auth.dependencies import require_role, get_uid
from services.oracle_client import oracle_client


router = APIRouter(
    prefix='/admin',
    tags=['admin'],
    dependencies=[Depends(require_role('admin'))]
)


class SetRoleRequest(BaseModel):
    telegram_id: str
    role: str
    subscription_days: int = 0


class BroadcastRequest(BaseModel):
    text: str


class PromptRequest(BaseModel):
    name: str
    content: str
    description: str = ""
    is_active: bool = True


class LLMConfigRequest(BaseModel):
    provider: str  # 'openai' or 'gemini'
    model: str
    temperature: float = 0.7
    max_tokens: int = 4000


@router.get('/stats')
async def get_stats():
    """Получение статистики пользователей"""
    try:
        # Получаем всех пользователей из Oracle AJD
        users = oracle_client.query_documents('users', limit=10000)

        # Подсчитываем по ролям
        role_counts = {
            'admin': 0,
            'moderator': 0,
            'vip': 0,
            'premium': 0,
            'user': 0
        }

        total_users = len(users)
        for user_data in users:
            role = user_data.get('role', 'user')
            if role in role_counts:
                role_counts[role] += 1

        # Получаем активные подписки
        subscriptions = oracle_client.query_documents('subscriptions', limit=10000)

        active_premium = 0
        active_vip = 0
        now = datetime.utcnow()

        for sub_data in subscriptions:
            expires_at_str = sub_data.get('expires_at')
            level = sub_data.get('level', '')

            if expires_at_str:
                try:
                    expires_at = datetime.fromisoformat(expires_at_str.replace('Z', '+00:00'))
                    if expires_at > now:
                        if level == 'premium':
                            active_premium += 1
                        elif level == 'vip':
                            active_vip += 1
                except ValueError:
                    continue

        return {
            'total_users': total_users,
            'roles': role_counts,
            'active_subscriptions': {
                'premium': active_premium,
                'vip': active_vip
            }
        }

    except Exception as e:
        logger.error(f"Ошибка получения статистики: {e}")
        raise HTTPException(status_code=500, detail="Ошибка получения статистики")


@router.post('/set_role')
async def set_role(request: SetRoleRequest):
    """Установка роли пользователю"""
    try:
        # Проверяем валидность роли
        valid_roles = ['user', 'premium', 'vip', 'moderator', 'admin']
        if request.role not in valid_roles:
            raise HTTPException(status_code=400, detail=f"Неверная роль. Доступные: {valid_roles}")

        # Получаем существующего пользователя
        user_data = oracle_client.get_document('users', request.telegram_id)

        if not user_data:
            raise HTTPException(status_code=404, detail="Пользователь не найден")

        # Обновляем роль пользователя
        user_data['role'] = request.role
        user_data['updated_at'] = datetime.utcnow().isoformat()

        success = oracle_client.update_document('users', request.telegram_id, user_data)
        if not success:
            raise HTTPException(status_code=500, detail="Ошибка обновления роли")

        # Если указаны дни подписки, создаем/обновляем подписку
        if request.subscription_days > 0:
            expires_at = datetime.utcnow() + timedelta(days=request.subscription_days)

            subscription_data = {
                'telegram_id': request.telegram_id,
                'level': request.role if request.role in ['premium', 'vip'] else 'premium',
                'expires_at': expires_at.isoformat(),
                'created_at': datetime.utcnow().isoformat()
            }

            oracle_client.insert_document('subscriptions', request.telegram_id, subscription_data)

        logger.info(f"Роль {request.role} установлена пользователю {request.telegram_id}")

        return {
            'success': True,
            'message': f'Роль {request.role} установлена пользователю {request.telegram_id}'
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Ошибка установки роли: {e}")
        raise HTTPException(status_code=500, detail="Ошибка установки роли")


@router.post('/broadcast')
async def broadcast_message(request: BroadcastRequest):
    """Рассылка сообщения всем пользователям"""
    try:
        # Получаем всех пользователей из Oracle AJD
        users = oracle_client.query_documents('users', limit=10000)

        user_ids = []
        for user_data in users:
            telegram_id = user_data.get('telegram_id') or user_data.get('_id')
            if telegram_id:
                user_ids.append(telegram_id)

        # Создаем уникальный ID для рассылки
        import uuid
        broadcast_id = str(uuid.uuid4())

        # Сохраняем задачу на рассылку в очередь
        broadcast_data = {
            'text': request.text,
            'user_ids': user_ids,
            'status': 'pending',
            'created_at': datetime.utcnow().isoformat()
        }

        success = oracle_client.insert_document('broadcast_queue', broadcast_id, broadcast_data)
        if not success:
            raise HTTPException(status_code=500, detail="Ошибка создания задачи рассылки")

        logger.info(f"Создана задача на рассылку для {len(user_ids)} пользователей")

        return {
            'success': True,
            'message': f'Рассылка запланирована для {len(user_ids)} пользователей',
            'broadcast_id': broadcast_id
        }

    except Exception as e:
        logger.error(f"Ошибка создания рассылки: {e}")
        raise HTTPException(status_code=500, detail="Ошибка создания рассылки")


@router.post('/gc')
async def garbage_collect():
    """Ручная очистка устаревших данных"""
    try:
        cutoff_date = datetime.utcnow() - timedelta(days=30)
        flags_cutoff = datetime.utcnow() - timedelta(days=14)
        now = datetime.utcnow()

        deleted_analyses = 0
        deleted_flags = 0
        deleted_bans = 0

        # Удаляем анализы старше 30 дней
        try:
            analyses = oracle_client.query_documents('analyses', limit=10000)
            for analysis in analyses:
                created_at_str = analysis.get('created_at')
                if created_at_str:
                    try:
                        created_at = datetime.fromisoformat(created_at_str.replace('Z', '+00:00'))
                        if created_at < cutoff_date:
                            analysis_id = analysis.get('_id')
                            if analysis_id and oracle_client.delete_document('analyses', analysis_id):
                                deleted_analyses += 1
                    except ValueError:
                        continue
        except Exception as e:
            logger.warning(f"Ошибка очистки анализов: {e}")

        # Удаляем флаги старше 14 дней
        try:
            flags = oracle_client.query_documents('flags', limit=10000)
            for flag in flags:
                ts_str = flag.get('ts') or flag.get('created_at')
                if ts_str:
                    try:
                        ts = datetime.fromisoformat(ts_str.replace('Z', '+00:00'))
                        if ts < flags_cutoff:
                            flag_id = flag.get('_id')
                            if flag_id and oracle_client.delete_document('flags', flag_id):
                                deleted_flags += 1
                    except ValueError:
                        continue
        except Exception as e:
            logger.warning(f"Ошибка очистки флагов: {e}")

        # Удаляем истекшие баны
        try:
            bans = oracle_client.query_documents('bans', limit=10000)
            for ban in bans:
                expires_at_str = ban.get('expires_at')
                if expires_at_str:
                    try:
                        expires_at = datetime.fromisoformat(expires_at_str.replace('Z', '+00:00'))
                        if expires_at < now:
                            ban_id = ban.get('_id')
                            if ban_id and oracle_client.delete_document('bans', ban_id):
                                deleted_bans += 1
                    except ValueError:
                        continue
        except Exception as e:
            logger.warning(f"Ошибка очистки банов: {e}")

        logger.info(f"Очистка завершена: анализы={deleted_analyses}, флаги={deleted_flags}, баны={deleted_bans}")

        return {
            'success': True,
            'deleted': {
                'analyses': deleted_analyses,
                'flags': deleted_flags,
                'bans': deleted_bans
            }
        }

    except Exception as e:
        logger.error(f"Ошибка очистки данных: {e}")
        raise HTTPException(status_code=500, detail="Ошибка очистки данных")


# === PROMPTS MANAGEMENT ===

@router.get('/prompts')
async def get_prompts():
    """Получение всех промптов"""
    try:
        prompts = oracle_client.query_documents('prompts', limit=1000)
        return {'prompts': prompts}
    except Exception as e:
        logger.error(f"Ошибка получения промптов: {e}")
        raise HTTPException(status_code=500, detail="Ошибка получения промптов")


@router.post('/prompts')
async def create_prompt(prompt: PromptRequest):
    """Создание нового промпта"""
    try:
        prompt_data = {
            'name': prompt.name,
            'content': prompt.content,
            'description': prompt.description,
            'is_active': prompt.is_active,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }

        result = oracle_client.create_document('prompts', prompt_data)
        return {'success': True, 'prompt': result}
    except Exception as e:
        logger.error(f"Ошибка создания промпта: {e}")
        raise HTTPException(status_code=500, detail="Ошибка создания промпта")


@router.put('/prompts/{prompt_id}')
async def update_prompt(prompt_id: str, prompt: PromptRequest):
    """Обновление промпта"""
    try:
        prompt_data = {
            'name': prompt.name,
            'content': prompt.content,
            'description': prompt.description,
            'is_active': prompt.is_active,
            'updated_at': datetime.now().isoformat()
        }

        result = oracle_client.update_document('prompts', prompt_id, prompt_data)
        return {'success': True, 'prompt': result}
    except Exception as e:
        logger.error(f"Ошибка обновления промпта: {e}")
        raise HTTPException(status_code=500, detail="Ошибка обновления промпта")


@router.delete('/prompts/{prompt_id}')
async def delete_prompt(prompt_id: str):
    """Удаление промпта"""
    try:
        result = oracle_client.delete_document('prompts', prompt_id)
        return {'success': True, 'deleted': result}
    except Exception as e:
        logger.error(f"Ошибка удаления промпта: {e}")
        raise HTTPException(status_code=500, detail="Ошибка удаления промпта")


# === LLM CONFIGURATION ===

@router.get('/llm-config')
async def get_llm_config():
    """Получение конфигурации LLM"""
    try:
        config = oracle_client.query_documents('config',
                                             filter_conditions={'type': 'llm'},
                                             limit=1)

        if config:
            return config[0]
        else:
            # Возвращаем конфигурацию по умолчанию
            return {
                'provider': 'gemini',
                'model': 'gemini-2.5-pro',
                'temperature': 0.7,
                'max_tokens': 4000
            }
    except Exception as e:
        logger.error(f"Ошибка получения конфигурации LLM: {e}")
        raise HTTPException(status_code=500, detail="Ошибка получения конфигурации")


@router.post('/llm-config')
async def update_llm_config(config: LLMConfigRequest):
    """Обновление конфигурации LLM"""
    try:
        config_data = {
            'type': 'llm',
            'provider': config.provider,
            'model': config.model,
            'temperature': config.temperature,
            'max_tokens': config.max_tokens,
            'updated_at': datetime.now().isoformat()
        }

        # Проверяем, есть ли уже конфигурация
        existing = oracle_client.query_documents('config',
                                               filter_conditions={'type': 'llm'},
                                               limit=1)

        if existing:
            # Обновляем существующую
            config_id = existing[0].get('id')
            result = oracle_client.update_document('config', config_id, config_data)
        else:
            # Создаем новую
            result = oracle_client.create_document('config', config_data)

        return {'success': True, 'config': result}
    except Exception as e:
        logger.error(f"Ошибка обновления конфигурации LLM: {e}")
        raise HTTPException(status_code=500, detail="Ошибка обновления конфигурации")
