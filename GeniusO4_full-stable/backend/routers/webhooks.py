# backend/routers/webhooks.py

import logging
from typing import Dict, Any
from fastapi import APIRouter, Request, HTTPException, Header
from datetime import datetime

from services.oracle_client import get_oracle_client
from config.config import logger

router = APIRouter(prefix="/api/webhooks", tags=["webhooks"])

# Wallet Pay webhook удален - используем только Telegram Payments

# Wallet Pay обработчики удалены

# Старые Stripe обработчики удалены - заменены на Wallet Pay

@router.get("/test")
async def test_webhooks():
    """Тестовый endpoint для проверки webhook системы"""
    try:
        return {
            "success": True,
            "message": "Webhook system is working",
            "supported_payments": ["telegram_stars"],
            "endpoints": [
                "/api/webhooks/telegram/payments"
            ]
        }

    except Exception as e:
        logger.error(f"Ошибка тестирования webhook системы: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@router.post("/telegram/payments")
async def telegram_payments_webhook(request: Request):
    """
    Webhook для обработки Telegram Payments (если нужен)
    
    Args:
        request: HTTP запрос с данными от Telegram
        
    Returns:
        Подтверждение обработки webhook
    """
    try:
        # Получаем данные от Telegram
        data = await request.json()
        
        logger.info(f"Получен Telegram payments webhook: {data}")
        
        # Здесь можно добавить дополнительную логику обработки
        # Telegram Payments обычно обрабатываются через bot handlers
        
        return {"success": True}
        
    except Exception as e:
        logger.error(f"Ошибка обработки Telegram payments webhook: {e}")
        raise HTTPException(status_code=500, detail="Webhook processing failed")
