# backend/routers/config.py

import logging
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field

from services.llm_router import llm_router, LLMProvider
from services.redis_client import redis_client
from middleware.cache_middleware import CacheControl
from auth.dependencies import get_current_user

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/config", tags=["configuration"])

class LLMSwitchRequest(BaseModel):
    """Модель запроса переключения LLM провайдера"""
    provider: str = Field(..., description="LLM провайдер: openai или gemini")

class CacheControlRequest(BaseModel):
    """Модель запроса управления кэшем"""
    action: str = Field(..., description="Действие: clear_all, clear_ohlcv, enable, disable")

@router.get("/llm/status")
async def get_llm_status():
    """
    Получение статуса LLM провайдеров
    
    Returns:
        Статус всех LLM провайдеров
    """
    try:
        status = llm_router.get_providers_status()
        return {
            "success": True,
            "data": status
        }
    except Exception as e:
        logger.error(f"Ошибка получения статуса LLM: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/llm")
async def switch_llm_provider(
    request: LLMSwitchRequest,
    user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Переключение LLM провайдера
    
    Args:
        request: Запрос с новым провайдером
        user: Текущий пользователь (должен быть админом)
        
    Returns:
        Результат переключения
    """
    try:
        # Проверяем права доступа
        if user.get("role") != "admin":
            raise HTTPException(
                status_code=403, 
                detail="Только администраторы могут переключать LLM провайдеры"
            )
        
        # Валидируем провайдер
        try:
            provider = LLMProvider(request.provider.lower())
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail=f"Неподдерживаемый провайдер: {request.provider}. Доступны: openai, gemini"
            )
        
        # Переключаем провайдер
        result = llm_router.switch_provider(provider)
        
        if result["success"]:
            logger.info(f"LLM провайдер переключен пользователем {user.get('telegram_id')}: {result}")
            return {
                "success": True,
                "message": f"LLM провайдер переключен на {provider.value}",
                "data": result
            }
        else:
            raise HTTPException(status_code=400, detail=result.get("error", "Неизвестная ошибка"))
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Ошибка переключения LLM провайдера: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/llm/test")
async def test_llm_providers(user: Dict[str, Any] = Depends(get_current_user)):
    """
    Тестирование всех LLM провайдеров
    
    Args:
        user: Текущий пользователь (должен быть админом)
        
    Returns:
        Результаты тестирования
    """
    try:
        # Проверяем права доступа
        if user.get("role") != "admin":
            raise HTTPException(
                status_code=403,
                detail="Только администраторы могут тестировать LLM провайдеры"
            )
        
        # Тестируем провайдеры
        test_results = await llm_router.test_all_providers()
        
        return {
            "success": True,
            "data": test_results
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Ошибка тестирования LLM провайдеров: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/cache/status")
async def get_cache_status():
    """
    Получение статуса кэша
    
    Returns:
        Статистика кэша
    """
    try:
        stats = CacheControl.get_cache_stats()
        return {
            "success": True,
            "data": stats
        }
    except Exception as e:
        logger.error(f"Ошибка получения статуса кэша: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/cache")
async def manage_cache(
    request: CacheControlRequest,
    user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Управление кэшем
    
    Args:
        request: Запрос управления кэшем
        user: Текущий пользователь (должен быть админом)
        
    Returns:
        Результат операции
    """
    try:
        # Проверяем права доступа
        if user.get("role") != "admin":
            raise HTTPException(
                status_code=403,
                detail="Только администраторы могут управлять кэшем"
            )
        
        action = request.action.lower()
        result = {"success": False, "message": "Неизвестное действие"}
        
        if action == "clear_all":
            deleted_count = CacheControl.clear_all_http_cache()
            result = {
                "success": True,
                "message": f"Очищено {deleted_count} ключей из HTTP кэша",
                "deleted_keys": deleted_count
            }
            
        elif action == "clear_ohlcv":
            deleted_count = CacheControl.clear_ohlcv_cache()
            result = {
                "success": True,
                "message": f"Очищено {deleted_count} ключей OHLCV кэша",
                "deleted_keys": deleted_count
            }
            
        elif action == "enable":
            # Включение кэша (через переменную окружения или Redis флаг)
            redis_client.set_flag("cache_enabled", True)
            result = {
                "success": True,
                "message": "Кэш включен"
            }
            
        elif action == "disable":
            # Отключение кэша
            redis_client.set_flag("cache_enabled", False)
            result = {
                "success": True,
                "message": "Кэш отключен"
            }
            
        else:
            raise HTTPException(
                status_code=400,
                detail=f"Неподдерживаемое действие: {action}. Доступны: clear_all, clear_ohlcv, enable, disable"
            )
        
        logger.info(f"Операция с кэшем выполнена пользователем {user.get('telegram_id')}: {action}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Ошибка управления кэшем: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/system/info")
async def get_system_info():
    """
    Получение информации о системе
    
    Returns:
        Системная информация
    """
    try:
        import os
        import sys
        from datetime import datetime
        
        system_info = {
            "python_version": sys.version,
            "environment": os.getenv("ENVIRONMENT", "unknown"),
            "current_time": datetime.utcnow().isoformat(),
            "llm_status": llm_router.get_providers_status(),
            "cache_status": CacheControl.get_cache_stats(),
            "redis_connected": redis_client.is_connected()
        }
        
        return {
            "success": True,
            "data": system_info
        }
        
    except Exception as e:
        logger.error(f"Ошибка получения системной информации: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/health")
async def health_check():
    """
    Проверка здоровья конфигурационного сервиса
    
    Returns:
        Статус здоровья
    """
    try:
        health_status = {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "services": {
                "llm_router": llm_router.current_provider.value if llm_router.current_provider else "unknown",
                "redis": redis_client.is_connected(),
                "oracle": True  # Предполагаем что Oracle доступен если мы дошли до этой точки
            }
        }
        
        # Проверяем критические сервисы
        all_healthy = all(health_status["services"].values())
        if not all_healthy:
            health_status["status"] = "degraded"
        
        return {
            "success": True,
            "data": health_status
        }
        
    except Exception as e:
        logger.error(f"Ошибка проверки здоровья: {e}")
        return {
            "success": False,
            "error": str(e),
            "status": "unhealthy"
        }
