# backend/migrations/firestore_to_oracle.py

import os
import sys
import logging
from typing import Dict, Any, List
from datetime import datetime

# Добавляем путь к backend для импортов
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from google.cloud import firestore
from backend.services.oracle_client import oracle_client
from backend.config.config import logger

class FirestoreToOracleMigration:
    """
    Миграция данных из Firestore в Oracle Autonomous JSON Database
    """
    
    def __init__(self):
        self.firestore_client = None
        self.oracle_client = oracle_client
        self._init_firestore()
    
    def _init_firestore(self):
        """Инициализация Firestore клиента"""
        try:
            self.firestore_client = firestore.Client()
            logger.info("Firestore клиент инициализирован")
        except Exception as e:
            logger.error(f"Ошибка инициализации Firestore: {e}")
            raise
    
    def migrate_collection(self, collection_name: str) -> Dict[str, int]:
        """
        Миграция коллекции из Firestore в Oracle
        
        Args:
            collection_name: Имя коллекции для миграции
            
        Returns:
            Dict с статистикой миграции
        """
        stats = {
            "total": 0,
            "success": 0,
            "failed": 0,
            "errors": []
        }
        
        try:
            logger.info(f"Начинаем миграцию коллекции {collection_name}")
            
            # Получаем все документы из Firestore
            collection_ref = self.firestore_client.collection(collection_name)
            docs = collection_ref.stream()
            
            for doc in docs:
                stats["total"] += 1
                doc_id = doc.id
                doc_data = doc.to_dict()
                
                try:
                    # Преобразуем данные для Oracle
                    oracle_data = self._convert_firestore_to_oracle(doc_data)
                    
                    # Вставляем в Oracle
                    success = self.oracle_client.insert_document(
                        collection_name, 
                        doc_id, 
                        oracle_data
                    )
                    
                    if success:
                        stats["success"] += 1
                        logger.debug(f"Документ {doc_id} успешно мигрирован")
                    else:
                        stats["failed"] += 1
                        stats["errors"].append(f"Не удалось вставить документ {doc_id}")
                        
                except Exception as e:
                    stats["failed"] += 1
                    error_msg = f"Ошибка миграции документа {doc_id}: {e}"
                    stats["errors"].append(error_msg)
                    logger.error(error_msg)
            
            logger.info(f"Миграция коллекции {collection_name} завершена. "
                       f"Всего: {stats['total']}, Успешно: {stats['success']}, "
                       f"Ошибок: {stats['failed']}")
            
        except Exception as e:
            error_msg = f"Критическая ошибка миграции коллекции {collection_name}: {e}"
            stats["errors"].append(error_msg)
            logger.error(error_msg)
        
        return stats
    
    def _convert_firestore_to_oracle(self, firestore_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Преобразование данных из формата Firestore в формат Oracle JSON
        
        Args:
            firestore_data: Данные из Firestore
            
        Returns:
            Преобразованные данные для Oracle
        """
        oracle_data = {}
        
        for key, value in firestore_data.items():
            # Преобразуем datetime объекты в строки
            if isinstance(value, datetime):
                oracle_data[key] = value.isoformat()
            # Преобразуем Firestore Timestamp в строку
            elif hasattr(value, 'timestamp'):
                oracle_data[key] = datetime.fromtimestamp(value.timestamp()).isoformat()
            # Рекурсивно обрабатываем вложенные словари
            elif isinstance(value, dict):
                oracle_data[key] = self._convert_firestore_to_oracle(value)
            # Обрабатываем списки
            elif isinstance(value, list):
                oracle_data[key] = [
                    self._convert_firestore_to_oracle(item) if isinstance(item, dict) else item
                    for item in value
                ]
            else:
                oracle_data[key] = value
        
        return oracle_data
    
    def migrate_all_collections(self) -> Dict[str, Dict[str, int]]:
        """
        Миграция всех основных коллекций
        
        Returns:
            Статистика миграции по всем коллекциям
        """
        collections_to_migrate = ["users", "config", "prompts", "subscriptions"]
        migration_stats = {}
        
        logger.info("Начинаем полную миграцию из Firestore в Oracle AJD")
        
        for collection_name in collections_to_migrate:
            try:
                stats = self.migrate_collection(collection_name)
                migration_stats[collection_name] = stats
            except Exception as e:
                logger.error(f"Критическая ошибка миграции коллекции {collection_name}: {e}")
                migration_stats[collection_name] = {
                    "total": 0,
                    "success": 0,
                    "failed": 0,
                    "errors": [str(e)]
                }
        
        # Выводим общую статистику
        self._print_migration_summary(migration_stats)
        
        return migration_stats
    
    def _print_migration_summary(self, stats: Dict[str, Dict[str, int]]):
        """Вывод сводки по миграции"""
        logger.info("=== СВОДКА МИГРАЦИИ ===")
        
        total_docs = 0
        total_success = 0
        total_failed = 0
        
        for collection, collection_stats in stats.items():
            total_docs += collection_stats["total"]
            total_success += collection_stats["success"]
            total_failed += collection_stats["failed"]
            
            logger.info(f"{collection}: {collection_stats['success']}/{collection_stats['total']} успешно")
            
            if collection_stats["errors"]:
                logger.warning(f"Ошибки в {collection}:")
                for error in collection_stats["errors"][:5]:  # Показываем первые 5 ошибок
                    logger.warning(f"  - {error}")
        
        logger.info(f"ИТОГО: {total_success}/{total_docs} документов мигрировано успешно")
        if total_failed > 0:
            logger.warning(f"Не удалось мигрировать {total_failed} документов")
    
    def verify_migration(self, collection_name: str) -> bool:
        """
        Проверка успешности миграции коллекции
        
        Args:
            collection_name: Имя коллекции для проверки
            
        Returns:
            True если миграция прошла успешно
        """
        try:
            # Подсчитываем документы в Firestore
            firestore_count = len(list(self.firestore_client.collection(collection_name).stream()))
            
            # Подсчитываем документы в Oracle
            oracle_docs = self.oracle_client.query_documents(collection_name, limit=None)
            oracle_count = len(oracle_docs)
            
            logger.info(f"Проверка {collection_name}: Firestore={firestore_count}, Oracle={oracle_count}")
            
            return firestore_count == oracle_count
            
        except Exception as e:
            logger.error(f"Ошибка проверки миграции {collection_name}: {e}")
            return False

def main():
    """Основная функция для запуска миграции"""
    try:
        migration = FirestoreToOracleMigration()
        
        # Запускаем миграцию всех коллекций
        stats = migration.migrate_all_collections()
        
        # Проверяем результаты миграции
        logger.info("Проверяем результаты миграции...")
        for collection_name in ["users", "config", "prompts", "subscriptions"]:
            is_valid = migration.verify_migration(collection_name)
            status = "✅ УСПЕШНО" if is_valid else "❌ ОШИБКА"
            logger.info(f"Проверка {collection_name}: {status}")
        
        logger.info("Миграция завершена!")
        
    except Exception as e:
        logger.error(f"Критическая ошибка миграции: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
