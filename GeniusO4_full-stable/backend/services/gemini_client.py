# backend/services/gemini_client.py

import os
import logging
from typing import Dict, Any, List, Optional
import google.generativeai as genai
from google.auth.exceptions import DefaultCredentialsError
import google.auth
from dotenv import load_dotenv

load_dotenv()

logger = logging.getLogger(__name__)

class GeminiClient:
    """
    Клиент для работы с Google Gemini API
    """
    
    def __init__(self):
        # Доступные модели Gemini (2025)
        self.available_models = [
            "gemini-2.5-pro",
            "gemini-1.5-pro",
            "gemini-1.5-flash",
            "gemini-2.0-flash-exp"
        ]

        self.model_name = "gemini-2.5-pro"  # Новейшая модель
        self.model = None
        self.is_authenticated = False
        self._initialize_client()
    
    def _initialize_client(self):
        """Инициализация Gemini клиента"""
        try:
            # Пытаемся использовать Application Default Credentials (ADC)
            # Это работает если пользователь выполнил: gcloud auth application-default login
            credentials, project = google.auth.default()
            
            # Настраиваем Gemini API с использованием ADC
            genai.configure(credentials=credentials)
            
            # Инициализируем модель
            self.model = genai.GenerativeModel(self.model_name)
            
            self.is_authenticated = True
            logger.info(f"Gemini клиент успешно инициализирован с моделью {self.model_name}")
            
        except DefaultCredentialsError:
            logger.warning("Google Application Default Credentials не найдены")
            logger.warning("Выполните: gcloud auth application-default login")
            self.is_authenticated = False
            
        except Exception as e:
            logger.error(f"Ошибка инициализации Gemini клиента: {e}")
            self.is_authenticated = False
    
    def is_available(self) -> bool:
        """Проверка доступности Gemini API"""
        return self.is_authenticated and self.model is not None
    
    async def generate_analysis(self, prompt: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Генерация анализа с помощью Gemini
        
        Args:
            prompt: Промпт для анализа
            context: Дополнительный контекст (данные OHLCV, индикаторы и т.д.)
            
        Returns:
            Результат анализа от Gemini
        """
        if not self.is_available():
            raise RuntimeError("Gemini API недоступен. Проверьте аутентификацию.")
        
        try:
            # Подготавливаем полный промпт с контекстом
            full_prompt = self._prepare_prompt(prompt, context)
            
            # Генерируем ответ
            response = await self.model.generate_content_async(
                full_prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.7,
                    top_p=0.8,
                    top_k=40,
                    max_output_tokens=2048,
                )
            )
            
            # Обрабатываем ответ
            if response.text:
                result = {
                    "success": True,
                    "content": response.text,
                    "model": self.model_name,
                    "provider": "gemini",
                    "usage": {
                        "prompt_tokens": len(full_prompt.split()),
                        "completion_tokens": len(response.text.split()),
                        "total_tokens": len(full_prompt.split()) + len(response.text.split())
                    }
                }
                
                logger.info(f"Gemini анализ выполнен успешно. Токенов: {result['usage']['total_tokens']}")
                return result
            else:
                raise ValueError("Пустой ответ от Gemini API")
                
        except Exception as e:
            logger.error(f"Ошибка генерации анализа Gemini: {e}")
            return {
                "success": False,
                "error": str(e),
                "provider": "gemini"
            }
    
    def _prepare_prompt(self, prompt: str, context: Dict[str, Any] = None) -> str:
        """
        Подготовка промпта с контекстом
        
        Args:
            prompt: Основной промпт
            context: Контекст с данными
            
        Returns:
            Полный промпт для Gemini
        """
        full_prompt = prompt
        
        if context:
            # Добавляем контекст OHLCV данных
            if "ohlcv_data" in context:
                ohlcv_summary = self._summarize_ohlcv(context["ohlcv_data"])
                full_prompt += f"\n\nДанные OHLCV:\n{ohlcv_summary}"
            
            # Добавляем технические индикаторы
            if "indicators" in context:
                indicators_summary = self._summarize_indicators(context["indicators"])
                full_prompt += f"\n\nТехнические индикаторы:\n{indicators_summary}"
            
            # Добавляем дополнительную информацию
            if "symbol" in context:
                full_prompt += f"\n\nАнализируемый актив: {context['symbol']}"
            
            if "timeframe" in context:
                full_prompt += f"\nТаймфрейм: {context['timeframe']}"
        
        return full_prompt
    
    def _summarize_ohlcv(self, ohlcv_data: List[Dict]) -> str:
        """Создание краткой сводки OHLCV данных"""
        if not ohlcv_data:
            return "Данные OHLCV отсутствуют"
        
        recent_data = ohlcv_data[-10:]  # Последние 10 свечей
        
        summary = "Последние 10 свечей:\n"
        for i, candle in enumerate(recent_data):
            summary += f"{i+1}. O:{candle.get('open', 'N/A')} H:{candle.get('high', 'N/A')} L:{candle.get('low', 'N/A')} C:{candle.get('close', 'N/A')} V:{candle.get('volume', 'N/A')}\n"
        
        return summary
    
    def _summarize_indicators(self, indicators: Dict[str, Any]) -> str:
        """Создание краткой сводки технических индикаторов"""
        if not indicators:
            return "Технические индикаторы отсутствуют"
        
        summary = "Активные индикаторы:\n"
        
        for indicator_name, indicator_data in indicators.items():
            if isinstance(indicator_data, dict):
                # Получаем последнее значение индикатора
                if "values" in indicator_data and indicator_data["values"]:
                    last_value = indicator_data["values"][-1]
                    summary += f"- {indicator_name}: {last_value}\n"
                elif "signal" in indicator_data:
                    summary += f"- {indicator_name}: {indicator_data['signal']}\n"
                else:
                    summary += f"- {indicator_name}: активен\n"
            else:
                summary += f"- {indicator_name}: {indicator_data}\n"
        
        return summary
    
    def get_model_info(self) -> Dict[str, Any]:
        """Получение информации о модели"""
        return {
            "provider": "gemini",
            "model": self.model_name,
            "available": self.is_available(),
            "authenticated": self.is_authenticated
        }
    
    async def test_connection(self) -> Dict[str, Any]:
        """Тестирование соединения с Gemini API"""
        if not self.is_available():
            return {
                "success": False,
                "error": "Gemini API недоступен",
                "authenticated": self.is_authenticated
            }
        
        try:
            # Простой тестовый запрос
            test_prompt = "Ответь одним словом: работает"
            response = await self.model.generate_content_async(test_prompt)
            
            return {
                "success": True,
                "response": response.text,
                "model": self.model_name,
                "authenticated": self.is_authenticated
            }
            
        except Exception as e:
            logger.error(f"Ошибка тестирования Gemini API: {e}")
            return {
                "success": False,
                "error": str(e),
                "authenticated": self.is_authenticated
            }

# Глобальный экземпляр Gemini клиента
gemini_client = GeminiClient()
