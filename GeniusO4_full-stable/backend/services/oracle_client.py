# backend/services/oracle_client.py

import json
import logging
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
import oracledb
from config.oracle_config import oracle_config

logger = logging.getLogger(__name__)

class OracleJSONClient:
    """
    Клиент для работы с Oracle Autonomous JSON Database
    """
    
    def __init__(self):
        self.pool = None
        self._initialize_pool()
        self._create_collections()
    
    def _initialize_pool(self):
        """Инициализация пула соединений"""
        try:
            # НЕ вызываем init_oracle_client() для использования thin mode
            # Thin mode не требует установки Oracle Client

            # Создание пула соединений в thin mode
            pool_params = oracle_config.get_pool_params()
            self.pool = oracledb.create_pool(**pool_params)

            logger.info("Oracle connection pool успешно создан в thin mode")

        except Exception as e:
            logger.error(f"Ошибка создания Oracle connection pool: {e}")
            raise
    
    def _get_connection(self):
        """Получить соединение из пула"""
        if not self.pool:
            raise RuntimeError("Oracle connection pool не инициализирован")
        return self.pool.acquire()
    
    def _create_collections(self):
        """Создание коллекций (таблиц) для JSON документов"""
        collections = {
            "users": """
                CREATE TABLE IF NOT EXISTS users (
                    id VARCHAR2(50) PRIMARY KEY,
                    data CLOB CHECK (data IS JSON),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """,
            "config": """
                CREATE TABLE IF NOT EXISTS config (
                    id VARCHAR2(50) PRIMARY KEY,
                    data CLOB CHECK (data IS JSON),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """,
            "prompts": """
                CREATE TABLE IF NOT EXISTS prompts (
                    id VARCHAR2(50) PRIMARY KEY,
                    data CLOB CHECK (data IS JSON),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """,
            "subscriptions": """
                CREATE TABLE IF NOT EXISTS subscriptions (
                    id VARCHAR2(50) PRIMARY KEY,
                    data CLOB CHECK (data IS JSON),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """
        }
        
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                for table_name, create_sql in collections.items():
                    try:
                        cursor.execute(create_sql)
                        logger.info(f"Таблица {table_name} создана или уже существует")
                    except Exception as e:
                        logger.warning(f"Ошибка создания таблицы {table_name}: {e}")
                conn.commit()
                
        except Exception as e:
            logger.error(f"Ошибка создания коллекций: {e}")
    
    def insert_document(self, collection: str, doc_id: str, data: Dict[str, Any]) -> bool:
        """Вставка JSON документа"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                json_data = json.dumps(data, ensure_ascii=False, default=str)
                
                sql = f"""
                    INSERT INTO {collection} (id, data, created_at, updated_at)
                    VALUES (:id, :data, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                """
                
                cursor.execute(sql, {"id": doc_id, "data": json_data})
                conn.commit()
                
                logger.info(f"Документ {doc_id} вставлен в коллекцию {collection}")
                return True
                
        except Exception as e:
            logger.error(f"Ошибка вставки документа {doc_id} в {collection}: {e}")
            return False
    
    def get_document(self, collection: str, doc_id: str) -> Optional[Dict[str, Any]]:
        """Получение JSON документа по ID"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                sql = f"SELECT data FROM {collection} WHERE id = :id"
                cursor.execute(sql, {"id": doc_id})
                
                result = cursor.fetchone()
                if result:
                    return json.loads(result[0])
                return None
                
        except Exception as e:
            logger.error(f"Ошибка получения документа {doc_id} из {collection}: {e}")
            return None
    
    def update_document(self, collection: str, doc_id: str, data: Dict[str, Any]) -> bool:
        """Обновление JSON документа"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                json_data = json.dumps(data, ensure_ascii=False, default=str)
                
                sql = f"""
                    UPDATE {collection} 
                    SET data = :data, updated_at = CURRENT_TIMESTAMP
                    WHERE id = :id
                """
                
                cursor.execute(sql, {"id": doc_id, "data": json_data})
                conn.commit()
                
                if cursor.rowcount > 0:
                    logger.info(f"Документ {doc_id} обновлен в коллекции {collection}")
                    return True
                else:
                    logger.warning(f"Документ {doc_id} не найден в коллекции {collection}")
                    return False
                
        except Exception as e:
            logger.error(f"Ошибка обновления документа {doc_id} в {collection}: {e}")
            return False
    
    def delete_document(self, collection: str, doc_id: str) -> bool:
        """Удаление JSON документа"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                sql = f"DELETE FROM {collection} WHERE id = :id"
                cursor.execute(sql, {"id": doc_id})
                conn.commit()
                
                if cursor.rowcount > 0:
                    logger.info(f"Документ {doc_id} удален из коллекции {collection}")
                    return True
                else:
                    logger.warning(f"Документ {doc_id} не найден в коллекции {collection}")
                    return False
                
        except Exception as e:
            logger.error(f"Ошибка удаления документа {doc_id} из {collection}: {e}")
            return False
    
    def query_documents(self, collection: str, query_filter: Dict[str, Any] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """Запрос JSON документов с фильтрацией"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                if query_filter:
                    # Простая фильтрация по JSON полям
                    conditions = []
                    params = {}
                    
                    for key, value in query_filter.items():
                        param_name = f"param_{len(params)}"
                        conditions.append(f"JSON_VALUE(data, '$.{key}') = :{param_name}")
                        params[param_name] = value
                    
                    where_clause = " AND ".join(conditions)
                    sql = f"SELECT id, data FROM {collection} WHERE {where_clause} ORDER BY created_at DESC"
                    if limit:
                        sql += f" FETCH FIRST {limit} ROWS ONLY"
                    
                    cursor.execute(sql, params)
                else:
                    sql = f"SELECT id, data FROM {collection} ORDER BY created_at DESC"
                    if limit:
                        sql += f" FETCH FIRST {limit} ROWS ONLY"
                    
                    cursor.execute(sql)
                
                results = []
                for row in cursor.fetchall():
                    doc_data = json.loads(row[1])
                    doc_data['_id'] = row[0]  # Добавляем ID документа
                    results.append(doc_data)
                
                return results
                
        except Exception as e:
            logger.error(f"Ошибка запроса документов из {collection}: {e}")
            return []
    
    def close(self):
        """Закрытие пула соединений"""
        if self.pool:
            self.pool.close()
            logger.info("Oracle connection pool закрыт")

# Глобальный экземпляр клиента (ленивая инициализация)
_oracle_client = None

def get_oracle_client():
    """Получить Oracle клиент с ленивой инициализацией"""
    global _oracle_client
    if _oracle_client is None:
        try:
            _oracle_client = OracleJSONClient()
        except Exception as e:
            logger.error(f"Ошибка инициализации Oracle клиента: {e}")
            _oracle_client = None
    return _oracle_client

# Для обратной совместимости - НЕ инициализируем при импорте!
# oracle_client будет инициализирован при первом обращении через get_oracle_client()
oracle_client = None
