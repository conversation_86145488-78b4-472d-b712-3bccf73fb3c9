# backend/services/redis_client.py

import os
import json
import logging
from typing import Any, Optional, Union
import redis
from dotenv import load_dotenv

load_dotenv()

logger = logging.getLogger(__name__)

class RedisClient:
    """
    Клиент для работы с Redis кэшем
    """
    
    def __init__(self):
        self.redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")
        self.redis_password = os.getenv("REDIS_PASSWORD")
        self.default_ttl = int(os.getenv("REDIS_TTL_OHLCV", "900"))  # 15 минут
        self.client = None
        self._connect()
    
    def _connect(self):
        """Подключение к Redis"""
        try:
            connection_params = {
                "decode_responses": True,
                "socket_connect_timeout": 5,
                "socket_timeout": 5,
                "retry_on_timeout": True
            }
            
            if self.redis_password:
                connection_params["password"] = self.redis_password
            
            self.client = redis.from_url(self.redis_url, **connection_params)
            
            # Проверяем соединение
            self.client.ping()
            logger.info("Redis клиент успешно подключен")
            
        except Exception as e:
            logger.error(f"Ошибка подключения к Redis: {e}")
            self.client = None
    
    def is_connected(self) -> bool:
        """Проверка соединения с Redis"""
        if not self.client:
            return False
        
        try:
            self.client.ping()
            return True
        except:
            return False
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """
        Сохранение значения в Redis
        
        Args:
            key: Ключ
            value: Значение (будет сериализовано в JSON)
            ttl: Время жизни в секундах (по умолчанию используется default_ttl)
            
        Returns:
            True если успешно сохранено
        """
        if not self.is_connected():
            logger.warning("Redis недоступен, пропускаем кэширование")
            return False
        
        try:
            # Сериализуем значение в JSON
            serialized_value = json.dumps(value, ensure_ascii=False, default=str)
            
            # Устанавливаем TTL
            expire_time = ttl if ttl is not None else self.default_ttl
            
            # Сохраняем в Redis
            result = self.client.setex(key, expire_time, serialized_value)
            
            if result:
                logger.debug(f"Значение сохранено в кэш: {key} (TTL: {expire_time}s)")
            
            return result
            
        except Exception as e:
            logger.error(f"Ошибка сохранения в Redis {key}: {e}")
            return False
    
    def get(self, key: str) -> Optional[Any]:
        """
        Получение значения из Redis
        
        Args:
            key: Ключ
            
        Returns:
            Десериализованное значение или None если не найдено
        """
        if not self.is_connected():
            return None
        
        try:
            serialized_value = self.client.get(key)
            
            if serialized_value is None:
                return None
            
            # Десериализуем из JSON
            value = json.loads(serialized_value)
            logger.debug(f"Значение получено из кэша: {key}")
            
            return value
            
        except Exception as e:
            logger.error(f"Ошибка получения из Redis {key}: {e}")
            return None
    
    def delete(self, key: str) -> bool:
        """
        Удаление значения из Redis
        
        Args:
            key: Ключ
            
        Returns:
            True если успешно удалено
        """
        if not self.is_connected():
            return False
        
        try:
            result = self.client.delete(key)
            if result:
                logger.debug(f"Ключ удален из кэша: {key}")
            return bool(result)
            
        except Exception as e:
            logger.error(f"Ошибка удаления из Redis {key}: {e}")
            return False
    
    def exists(self, key: str) -> bool:
        """
        Проверка существования ключа в Redis
        
        Args:
            key: Ключ
            
        Returns:
            True если ключ существует
        """
        if not self.is_connected():
            return False
        
        try:
            return bool(self.client.exists(key))
        except Exception as e:
            logger.error(f"Ошибка проверки существования ключа {key}: {e}")
            return False
    
    def set_flag(self, key: str, value: bool = True, ttl: Optional[int] = None) -> bool:
        """
        Установка флага (для signals on/off и т.д.)
        
        Args:
            key: Ключ флага
            value: Значение флага
            ttl: Время жизни в секундах
            
        Returns:
            True если успешно установлено
        """
        return self.set(key, value, ttl)
    
    def get_flag(self, key: str, default: bool = False) -> bool:
        """
        Получение флага
        
        Args:
            key: Ключ флага
            default: Значение по умолчанию
            
        Returns:
            Значение флага
        """
        value = self.get(key)
        if value is None:
            return default
        return bool(value)
    
    def increment(self, key: str, amount: int = 1, ttl: Optional[int] = None) -> Optional[int]:
        """
        Инкремент счетчика
        
        Args:
            key: Ключ счетчика
            amount: Значение для инкремента
            ttl: Время жизни в секундах
            
        Returns:
            Новое значение счетчика или None при ошибке
        """
        if not self.is_connected():
            return None
        
        try:
            # Инкрементируем
            new_value = self.client.incrby(key, amount)
            
            # Устанавливаем TTL если указан
            if ttl is not None:
                self.client.expire(key, ttl)
            
            return new_value
            
        except Exception as e:
            logger.error(f"Ошибка инкремента счетчика {key}: {e}")
            return None
    
    def get_keys_pattern(self, pattern: str) -> list:
        """
        Получение ключей по паттерну
        
        Args:
            pattern: Паттерн для поиска ключей (например, "user:*")
            
        Returns:
            Список найденных ключей
        """
        if not self.is_connected():
            return []
        
        try:
            return self.client.keys(pattern)
        except Exception as e:
            logger.error(f"Ошибка поиска ключей по паттерну {pattern}: {e}")
            return []
    
    def flush_all(self) -> bool:
        """
        Очистка всего кэша (осторожно!)
        
        Returns:
            True если успешно очищено
        """
        if not self.is_connected():
            return False
        
        try:
            self.client.flushall()
            logger.warning("Весь кэш Redis очищен")
            return True
        except Exception as e:
            logger.error(f"Ошибка очистки кэша Redis: {e}")
            return False
    
    def close(self):
        """Закрытие соединения с Redis"""
        if self.client:
            try:
                self.client.close()
                logger.info("Соединение с Redis закрыто")
            except Exception as e:
                logger.error(f"Ошибка закрытия соединения с Redis: {e}")

# Глобальный экземпляр Redis клиента (ленивая инициализация)
_redis_client = None

def get_redis_client():
    """Получить Redis клиент с ленивой инициализацией"""
    global _redis_client
    if _redis_client is None:
        try:
            _redis_client = RedisClient()
        except Exception as e:
            logger.error(f"Ошибка инициализации Redis клиента: {e}")
            _redis_client = None
    return _redis_client

# Для обратной совместимости - НЕ инициализируем при импорте!
# redis_client будет инициализирован при первом обращении через get_redis_client()
redis_client = None
