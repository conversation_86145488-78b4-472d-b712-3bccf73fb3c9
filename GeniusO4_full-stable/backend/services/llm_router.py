# backend/services/llm_router.py

import os
import logging
from typing import Dict, Any, Optional
from enum import Enum
from datetime import datetime

from services.gemini_client import gemini_client
from services.oracle_client import get_oracle_client
from services.redis_client import get_redis_client

logger = logging.getLogger(__name__)

class LLMProvider(Enum):
    """Доступные LLM провайдеры"""
    OPENAI = "openai"
    GEMINI = "gemini"

class LLMRouter:
    """
    Роутер для переключения между различными LLM провайдерами
    """
    
    def __init__(self):
        self.default_provider = LLMProvider(os.getenv("DEFAULT_LLM_PROVIDER", "openai"))
        self.current_provider = None
        self.openai_client = None
        self._initialize_openai()
        self._load_current_provider()
    
    def _initialize_openai(self):
        """Инициализация OpenAI клиента"""
        try:
            # Импортируем OpenAI клиент только если он нужен
            from services.chatgpt_analyzer import ChatGPTAnalyzer
            self.openai_client = ChatGPTAnalyzer()
            logger.info("OpenAI клиент инициализирован")
        except Exception as e:
            logger.error(f"Ошибка инициализации OpenAI клиента: {e}")
            self.openai_client = None
    
    def _load_current_provider(self):
        """Загрузка текущего провайдера из конфигурации"""
        try:
            # Сначала пытаемся получить из Redis (кэш)
            redis_client = get_redis_client()
            if redis_client:
                cached_provider = redis_client.get("llm_current_provider")
                if cached_provider:
                    self.current_provider = LLMProvider(cached_provider)
                    logger.info(f"Текущий LLM провайдер загружен из кэша: {self.current_provider.value}")
                    return
            
            # Если в кэше нет, загружаем из Oracle AJD
            oracle_client = get_oracle_client()
            if oracle_client:
                config_data = oracle_client.get_document('config', 'llm_switch')
                if config_data and 'provider' in config_data:
                    provider_name = config_data['provider']
                    self.current_provider = LLMProvider(provider_name)

                    # Кэшируем в Redis на 1 час
                    redis_client = get_redis_client()
                    if redis_client:
                        redis_client.set("llm_current_provider", provider_name, ttl=3600)
                
                logger.info(f"Текущий LLM провайдер загружен из БД: {self.current_provider.value}")
            else:
                # Используем провайдер по умолчанию
                self.current_provider = self.default_provider
                logger.info(f"Используется LLM провайдер по умолчанию: {self.current_provider.value}")
                
        except Exception as e:
            logger.error(f"Ошибка загрузки текущего провайдера: {e}")
            self.current_provider = self.default_provider
    
    def get_current_provider(self) -> LLMProvider:
        """Получение текущего провайдера"""
        if self.current_provider is None:
            self._load_current_provider()
        return self.current_provider
    
    def switch_provider(self, provider: LLMProvider) -> Dict[str, Any]:
        """
        Переключение LLM провайдера
        
        Args:
            provider: Новый провайдер
            
        Returns:
            Результат переключения
        """
        try:
            # Проверяем доступность провайдера
            if not self._is_provider_available(provider):
                return {
                    "success": False,
                    "error": f"Провайдер {provider.value} недоступен",
                    "current_provider": self.current_provider.value if self.current_provider else None
                }
            
            # Сохраняем в Oracle AJD
            config_data = {
                "provider": provider.value,
                "updated_at": datetime.utcnow().isoformat(),
                "updated_by": "system"
            }
            
            success = oracle_client.update_document('config', 'llm_switch', config_data)
            if not success:
                # Если обновление не удалось, пытаемся создать новый документ
                success = oracle_client.insert_document('config', 'llm_switch', config_data)
            
            if success:
                # Обновляем текущий провайдер
                old_provider = self.current_provider.value if self.current_provider else "none"
                self.current_provider = provider
                
                # Обновляем кэш в Redis
                redis_client.set("llm_current_provider", provider.value, ttl=3600)
                
                logger.info(f"LLM провайдер переключен: {old_provider} → {provider.value}")
                
                return {
                    "success": True,
                    "old_provider": old_provider,
                    "new_provider": provider.value,
                    "switched_at": config_data["updated_at"]
                }
            else:
                return {
                    "success": False,
                    "error": "Не удалось сохранить конфигурацию в БД",
                    "current_provider": self.current_provider.value if self.current_provider else None
                }
                
        except Exception as e:
            logger.error(f"Ошибка переключения LLM провайдера: {e}")
            return {
                "success": False,
                "error": str(e),
                "current_provider": self.current_provider.value if self.current_provider else None
            }
    
    def _is_provider_available(self, provider: LLMProvider) -> bool:
        """Проверка доступности провайдера"""
        if provider == LLMProvider.OPENAI:
            return self.openai_client is not None
        elif provider == LLMProvider.GEMINI:
            return gemini_client.is_available()
        return False
    
    async def generate_analysis(self, prompt: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Генерация анализа с использованием текущего провайдера
        
        Args:
            prompt: Промпт для анализа
            context: Контекст с данными
            
        Returns:
            Результат анализа
        """
        current_provider = self.get_current_provider()
        
        try:
            if current_provider == LLMProvider.OPENAI:
                return await self._generate_with_openai(prompt, context)
            elif current_provider == LLMProvider.GEMINI:
                return await self._generate_with_gemini(prompt, context)
            else:
                raise ValueError(f"Неподдерживаемый провайдер: {current_provider}")
                
        except Exception as e:
            logger.error(f"Ошибка генерации анализа с провайдером {current_provider.value}: {e}")
            
            # Пытаемся использовать резервный провайдер
            fallback_provider = self._get_fallback_provider(current_provider)
            if fallback_provider and self._is_provider_available(fallback_provider):
                logger.warning(f"Переключаемся на резервный провайдер: {fallback_provider.value}")
                
                if fallback_provider == LLMProvider.OPENAI:
                    return await self._generate_with_openai(prompt, context)
                elif fallback_provider == LLMProvider.GEMINI:
                    return await self._generate_with_gemini(prompt, context)
            
            # Если резервный провайдер тоже недоступен
            return {
                "success": False,
                "error": f"Провайдер {current_provider.value} недоступен, резервный провайдер также недоступен",
                "provider": current_provider.value
            }
    
    async def _generate_with_openai(self, prompt: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Генерация анализа с OpenAI"""
        if not self.openai_client:
            raise RuntimeError("OpenAI клиент не инициализирован")
        
        # Адаптируем вызов под существующий интерфейс ChatGPTAnalyzer
        # Предполагаем, что у него есть метод analyze_data
        result = await self.openai_client.analyze_data(prompt, context)
        
        # Добавляем информацию о провайдере
        if isinstance(result, dict):
            result["provider"] = "openai"
        
        return result
    
    async def _generate_with_gemini(self, prompt: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Генерация анализа с Gemini"""
        return await gemini_client.generate_analysis(prompt, context)
    
    def _get_fallback_provider(self, current_provider: LLMProvider) -> Optional[LLMProvider]:
        """Получение резервного провайдера"""
        if current_provider == LLMProvider.OPENAI:
            return LLMProvider.GEMINI
        elif current_provider == LLMProvider.GEMINI:
            return LLMProvider.OPENAI
        return None
    
    def get_providers_status(self) -> Dict[str, Any]:
        """Получение статуса всех провайдеров"""
        return {
            "current_provider": self.current_provider.value if self.current_provider else None,
            "default_provider": self.default_provider.value,
            "providers": {
                "openai": {
                    "available": self._is_provider_available(LLMProvider.OPENAI),
                    "client_initialized": self.openai_client is not None
                },
                "gemini": {
                    "available": self._is_provider_available(LLMProvider.GEMINI),
                    "authenticated": gemini_client.is_authenticated
                }
            }
        }
    
    async def test_all_providers(self) -> Dict[str, Any]:
        """Тестирование всех провайдеров"""
        results = {}
        
        # Тест OpenAI
        if self.openai_client:
            try:
                # Простой тест OpenAI (нужно адаптировать под реальный интерфейс)
                results["openai"] = {
                    "available": True,
                    "test_passed": True,
                    "error": None
                }
            except Exception as e:
                results["openai"] = {
                    "available": False,
                    "test_passed": False,
                    "error": str(e)
                }
        else:
            results["openai"] = {
                "available": False,
                "test_passed": False,
                "error": "Клиент не инициализирован"
            }
        
        # Тест Gemini
        gemini_test = await gemini_client.test_connection()
        results["gemini"] = gemini_test
        
        return {
            "current_provider": self.current_provider.value if self.current_provider else None,
            "tests": results
        }

# Глобальный экземпляр LLM роутера (lazy initialization)
_llm_router = None

def get_llm_router():
    """Получить экземпляр LLM роутера с lazy initialization"""
    global _llm_router
    if _llm_router is None:
        try:
            _llm_router = LLMRouter()
        except Exception as e:
            logger.error(f"Ошибка инициализации LLM роутера: {e}")
            _llm_router = None
    return _llm_router

# Для обратной совместимости
llm_router = None
