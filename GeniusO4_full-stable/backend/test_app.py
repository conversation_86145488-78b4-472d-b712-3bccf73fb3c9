from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

app = FastAPI(title="Test API")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["GET", "POST"],
    allow_headers=["*"],
)

@app.get("/health")
async def health():
    return {"status": "ok"}

@app.get("/api/test")
async def test_api():
    return {"message": "API работает!", "timestamp": "2025-07-02"}

@app.post("/api/analyze-test")
async def analyze_test():
    """Простой тестовый endpoint"""
    return {
        "analysis": {
            "primary_analysis": "Тестовый анализ показывает восходящий тренд",
            "recommendations": "Рекомендуется покупка"
        },
        "ohlc": [
            {
                "Open Time": "2024-01-01T00:00:00Z",
                "Close Time": "2024-01-01T04:00:00Z",
                "Open": 42000.0,
                "High": 42500.0,
                "Low": 41800.0,
                "Close": 42200.0,
                "Volume": 1000.0
            }
        ],
        "indicators": ["RSI", "MACD"]
    }

if __name__ == "__main__":
    uvicorn.run(app, host="127.0.0.1", port=8000)
