{"name": "<PERSON><PERSON><PERSON>-frontend", "version": "1.0.2", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest"}, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.3", "@mui/icons-material": "^5.17.1", "@mui/material": "^5.17.1", "@reduxjs/toolkit": "^2.8.2", "lightweight-charts": "^5.0.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^8.1.3", "react-router-dom": "^7.6.2", "redux": "^4.2.1"}, "devDependencies": {"@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^14.0.0", "@vitejs/plugin-react": "^4.1.0", "jsdom": "^22.1.0", "terser": "^5.36.0", "vite": "^6.3.5", "vitest": "^3.2.2"}}